import { supabase } from './supabase';
import { ConversationBoundaries, BlockedUser, ConversationReport, Profile } from '../types';
import { ErrorLogger } from './errorHandler';

// ====================================================================
// Conversation Boundaries Management
// ====================================================================

export async function getConversationBoundaries(userId: string): Promise<ConversationBoundaries | null> {
  try {
    const { data, error } = await supabase
      .from('conversation_boundaries')
      .select('*')
      .eq('user_id', userId)
      .single();

    if (error) {
      // Check if table doesn't exist (404 error)
      if (error.code === '42P01' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
        console.warn('Conversation boundaries table does not exist. Please run the database migration.');
        return null;
      }

      if (error.code === 'PGRST116') {
        // No boundaries found, create default ones
        return await createDefaultBoundaries(userId);
      }

      ErrorLogger.logError(error, 'database');
      return null;
    }

    return {
      ...data,
      custom_boundaries: Array.isArray(data.custom_boundaries) ? data.custom_boundaries : []
    };
  } catch (err: any) {
    // Handle network/table missing errors
    if (err?.message?.includes('404') || err?.status === 404) {
      console.warn('Conversation boundaries table does not exist. Please run the database migration.');
      return null;
    }

    ErrorLogger.logError(err, 'database');
    return null;
  }
}

export async function createDefaultBoundaries(userId: string): Promise<ConversationBoundaries | null> {
  try {
    const defaultBoundaries = {
      user_id: userId,
      allow_messages_from: 'everyone' as const,
      require_introduction: false,
      auto_decline_explicit: true,
      response_time_expectation: 'flexible' as const,
      availability_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone || 'UTC',
      block_explicit_content: true,
      block_personal_questions_early: false,
      block_meeting_requests_early: false,
      block_contact_sharing_early: true,
      custom_boundaries: []
    };

    const { data, error } = await supabase
      .from('conversation_boundaries')
      .insert(defaultBoundaries)
      .select()
      .single();

    if (error) {
      // Check if table doesn't exist
      if (error.code === '42P01' || error.message?.includes('relation') || error.message?.includes('does not exist')) {
        console.warn('Cannot create default boundaries: conversation_boundaries table does not exist.');
        return null;
      }

      ErrorLogger.logError(error, 'database');
      return null;
    }

    return {
      ...data,
      custom_boundaries: Array.isArray(data.custom_boundaries) ? data.custom_boundaries : []
    };
  } catch (err: any) {
    // Handle network/table missing errors
    if (err?.message?.includes('404') || err?.status === 404) {
      console.warn('Cannot create default boundaries: conversation_boundaries table does not exist.');
      return null;
    }

    ErrorLogger.logError(err, 'database');
    return null;
  }
}

export async function updateConversationBoundaries(
  userId: string, 
  boundaries: Partial<ConversationBoundaries>
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('conversation_boundaries')
      .update(boundaries)
      .eq('user_id', userId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to update conversation boundaries' };
  }
}

// ====================================================================
// Blocked Users Management
// ====================================================================

export async function getBlockedUsers(userId: string): Promise<BlockedUser[]> {
  try {
    // First check if the table exists by trying a simple count query
    const { error: tableCheckError } = await supabase
      .from('blocked_users')
      .select('count', { count: 'exact', head: true });

    if (tableCheckError) {
      // Check if table doesn't exist
      if (tableCheckError.code === '42P01' ||
          tableCheckError.message?.includes('relation') ||
          tableCheckError.message?.includes('does not exist')) {
        console.warn('Blocked users table does not exist. Feature not available.');
        return [];
      }

      ErrorLogger.logError(tableCheckError, 'database');
      return [];
    }

    const { data, error } = await supabase
      .from('blocked_users')
      .select(`
        *,
        blocked_user:profiles!blocked_users_blocked_id_fkey(
          id,
          username,
          full_name,
          avatar_url
        )
      `)
      .eq('blocker_id', userId)
      .order('created_at', { ascending: false });

    if (error) {
      ErrorLogger.logError(error, 'database');
      return [];
    }

    return data?.map(item => ({
      ...item,
      blocked_user: item.blocked_user ? {
        id: item.blocked_user.id,
        name: item.blocked_user.full_name || item.blocked_user.username || 'Unknown User',
        username: item.blocked_user.username || '',
        avatar_url: item.blocked_user.avatar_url,
        createdAt: new Date(),
        updatedAt: new Date()
      } as Profile : undefined
    })) || [];
  } catch (err: any) {
    // Handle network/table missing errors
    if (err?.message?.includes('404') || err?.status === 404) {
      console.warn('Blocked users feature is not available.');
      return [];
    }

    ErrorLogger.logError(err, 'database');
    return [];
  }
}

export async function blockUser(
  blockerId: string,
  blockedId: string,
  reason?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate input
    if (!blockerId || !blockedId) {
      return { success: false, error: 'Invalid user IDs provided' };
    }

    if (blockerId === blockedId) {
      return { success: false, error: 'Cannot block yourself' };
    }

    // Check if the table exists first
    const { error: tableCheckError } = await supabase
      .from('blocked_users')
      .select('count', { count: 'exact', head: true });

    if (tableCheckError) {
      if (tableCheckError.code === '42P01' ||
          tableCheckError.message?.includes('relation') ||
          tableCheckError.message?.includes('does not exist')) {
        return {
          success: false,
          error: 'Blocking feature is currently unavailable. Please try again later.'
        };
      }

      ErrorLogger.logError(tableCheckError, 'database');
      return { success: false, error: 'Unable to access blocking system' };
    }

    // Check if user is already blocked
    const { data: existingBlock } = await supabase
      .from('blocked_users')
      .select('id')
      .eq('blocker_id', blockerId)
      .eq('blocked_id', blockedId)
      .single();

    if (existingBlock) {
      return { success: false, error: 'User is already blocked' };
    }

    // Insert the block record
    const { error } = await supabase
      .from('blocked_users')
      .insert({
        blocker_id: blockerId,
        blocked_id: blockedId,
        reason: reason || null
      });

    if (error) {
      ErrorLogger.logError(error, 'database');

      // Handle specific error cases
      if (error.code === '23505') { // Unique constraint violation
        return { success: false, error: 'User is already blocked' };
      }

      return { success: false, error: 'Failed to block user. Please try again.' };
    }

    return { success: true };
  } catch (err: any) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'An unexpected error occurred while blocking the user' };
  }
}

export async function unblockUser(
  blockerId: string,
  blockedId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Validate input
    if (!blockerId || !blockedId) {
      return { success: false, error: 'Invalid user IDs provided' };
    }

    // Check if the table exists first
    const { error: tableCheckError } = await supabase
      .from('blocked_users')
      .select('count', { count: 'exact', head: true });

    if (tableCheckError) {
      if (tableCheckError.code === '42P01' ||
          tableCheckError.message?.includes('relation') ||
          tableCheckError.message?.includes('does not exist')) {
        return {
          success: false,
          error: 'Blocking feature is currently unavailable. Please try again later.'
        };
      }

      ErrorLogger.logError(tableCheckError, 'database');
      return { success: false, error: 'Unable to access blocking system' };
    }

    // Check if the block exists before trying to delete
    const { data: existingBlock } = await supabase
      .from('blocked_users')
      .select('id')
      .eq('blocker_id', blockerId)
      .eq('blocked_id', blockedId)
      .single();

    if (!existingBlock) {
      return { success: false, error: 'User is not currently blocked' };
    }

    // Delete the block record
    const { error } = await supabase
      .from('blocked_users')
      .delete()
      .eq('blocker_id', blockerId)
      .eq('blocked_id', blockedId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: 'Failed to unblock user. Please try again.' };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'An unexpected error occurred while unblocking the user' };
  }
}

/**
 * Check if a user is blocked by another user
 */
export async function isUserBlocked(
  blockerId: string,
  blockedId: string
): Promise<boolean> {
  try {
    // Check if the table exists first
    const { error: tableCheckError } = await supabase
      .from('blocked_users')
      .select('count', { count: 'exact', head: true });

    if (tableCheckError) {
      // If table doesn't exist, no one is blocked
      return false;
    }

    const { data, error } = await supabase
      .from('blocked_users')
      .select('id')
      .eq('blocker_id', blockerId)
      .eq('blocked_id', blockedId)
      .single();

    if (error && error.code !== 'PGRST116') { // PGRST116 = no rows returned
      ErrorLogger.logError(error, 'database');
      return false; // Default to not blocked on error
    }

    return !!data;
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return false; // Default to not blocked on error
  }
}

// ====================================================================
// Reporting System
// ====================================================================

export async function reportUser(
  reporterId: string,
  reportedUserId: string,
  reportType: ConversationReport['report_type'],
  description?: string,
  conversationId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('conversation_reports')
      .insert({
        reporter_id: reporterId,
        reported_user_id: reportedUserId,
        conversation_id: conversationId,
        report_type: reportType,
        description
      });

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to submit report' };
  }
}

// ====================================================================
// Utility Functions
// ====================================================================

export async function canUserMessage(
  senderId: string,
  recipientId: string
): Promise<{ canMessage: boolean; reason?: string }> {
  try {
    // Validate input
    if (!senderId || !recipientId) {
      return { canMessage: false, reason: 'Invalid user IDs' };
    }

    if (senderId === recipientId) {
      return { canMessage: true }; // Users can always message themselves
    }

    // Check if sender is blocked by recipient using our improved function
    const isBlocked = await isUserBlocked(recipientId, senderId);
    if (isBlocked) {
      return { canMessage: false, reason: 'You have been blocked by this user' };
    }

    // Check if recipient has blocked sender (reverse check)
    const hasBlockedRecipient = await isUserBlocked(senderId, recipientId);
    if (hasBlockedRecipient) {
      return { canMessage: false, reason: 'You have blocked this user' };
    }

    // Get recipient's boundaries
    const boundaries = await getConversationBoundaries(recipientId);
    if (!boundaries) {
      return { canMessage: true }; // Default to allowing if no boundaries set
    }

    // Check message permissions based on boundaries
    switch (boundaries.allow_messages_from) {
      case 'none':
        return { canMessage: false, reason: 'This user is not accepting messages' };

      case 'tagged_only':
        // Check if sender has tagged the recipient
        const { data: tagData } = await supabase
          .from('user_interest_tags')
          .select('id')
          .eq('tagger_id', senderId)
          .eq('tagged_user_id', recipientId)
          .single();

        if (!tagData) {
          return { canMessage: false, reason: 'This user only accepts messages from users who have tagged them' };
        }
        break;

      case 'mutual_interest':
        // Check for mutual tagging
        const { data: mutualTags } = await supabase
          .from('user_interest_tags')
          .select('id')
          .or(`and(tagger_id.eq.${senderId},tagged_user_id.eq.${recipientId}),and(tagger_id.eq.${recipientId},tagged_user_id.eq.${senderId})`);

        if (!mutualTags || mutualTags.length < 2) {
          return { canMessage: false, reason: 'This user only accepts messages from mutual interests' };
        }
        break;

      case 'everyone':
      default:
        // Allow messages from everyone
        break;
    }

    return { canMessage: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { canMessage: true }; // Default to allowing on error
  }
}

// Helper function to check if content violates boundaries
export function checkContentViolation(
  content: string,
  boundaries: ConversationBoundaries
): { isViolation: boolean; reason?: string } {
  const lowerContent = content.toLowerCase();

  if (boundaries.block_explicit_content) {
    const explicitKeywords = ['sex', 'sexual', 'nude', 'naked', 'porn', 'explicit'];
    if (explicitKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Message contains explicit content' };
    }
  }

  if (boundaries.block_personal_questions_early) {
    const personalKeywords = ['address', 'phone', 'number', 'where do you live', 'personal info'];
    if (personalKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Personal questions are not allowed early in conversations' };
    }
  }

  if (boundaries.block_meeting_requests_early) {
    const meetingKeywords = ['meet up', 'meet in person', 'coffee', 'date', 'hang out'];
    if (meetingKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Meeting requests are not allowed early in conversations' };
    }
  }

  if (boundaries.block_contact_sharing_early) {
    const contactKeywords = ['instagram', 'facebook', 'snapchat', 'whatsapp', 'telegram', '@'];
    if (contactKeywords.some(keyword => lowerContent.includes(keyword))) {
      return { isViolation: true, reason: 'Contact sharing is not allowed early in conversations' };
    }
  }

  return { isViolation: false };
}
