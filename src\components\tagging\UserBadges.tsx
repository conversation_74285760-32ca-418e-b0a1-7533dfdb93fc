import React, { useState, useEffect } from 'react';
import { MessageCircle, Users } from 'lucide-react';
import { getTagStats, getContactWillingness, TAG_TYPES, TagType } from '../../lib/userTagging';
import { ContactWillingnessStatus } from '../../types';

interface UserBadgesProps {
  userId: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  showContactStatus?: boolean;
  showInterestStats?: boolean;
}

const UserBadges: React.FC<UserBadgesProps> = ({
  userId,
  className = '',
  size = 'md',
  showContactStatus = true,
  showInterestStats = true
}) => {
  const [tagStats, setTagStats] = useState<Record<TagType, number>>({
    interested: 0,
    very_interested: 0,
    potential_match: 0
  });
  const [contactStatus, setContactStatus] = useState<ContactWillingnessStatus | null>(null);
  const [loading, setLoading] = useState(true);

  // Size configurations
  const sizeConfig = {
    sm: {
      badge: 'px-2 py-1 text-xs',
      icon: 'w-3 h-3',
      gap: 'gap-1'
    },
    md: {
      badge: 'px-2.5 py-1.5 text-sm',
      icon: 'w-4 h-4',
      gap: 'gap-2'
    },
    lg: {
      badge: 'px-3 py-2 text-base',
      icon: 'w-5 h-5',
      gap: 'gap-3'
    }
  };

  const config = sizeConfig[size];

  useEffect(() => {
    loadBadgeData();
  }, [userId]);

  const loadBadgeData = async () => {
    setLoading(true);
    try {
      const [stats, status] = await Promise.all([
        showInterestStats ? getTagStats(userId).catch(() => ({ interested: 0, very_interested: 0, potential_match: 0 })) : Promise.resolve({ interested: 0, very_interested: 0, potential_match: 0 }),
        showContactStatus ? getContactWillingness(userId).catch(() => null) : Promise.resolve(null)
      ]);

      setTagStats(stats);
      setContactStatus(status);
    } catch (err) {
      console.error('Failed to load badge data:', err);
      // Set default values on error
      setTagStats({ interested: 0, very_interested: 0, potential_match: 0 });
      setContactStatus(null);
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <div className={`flex ${config.gap} ${className}`}>
        <div className={`${config.badge} bg-gray-200 rounded-full animate-pulse`}>
          <div className="w-16 h-4 bg-gray-300 rounded"></div>
        </div>
      </div>
    );
  }

  const badges = [];

  // Contact willingness badge
  if (showContactStatus && contactStatus?.is_open_to_contact) {
    badges.push(
      <div
        key="contact-status"
        className={`
          ${config.badge} bg-green-100 text-green-800 rounded-full font-medium
          flex items-center ${config.gap} border border-green-200
        `}
        title={contactStatus.status_message || 'Open to contact'}
      >
        <MessageCircle className={config.icon} />
        <span>Open to Contact</span>
      </div>
    );
  }

  // Interest stats badges
  if (showInterestStats) {
    const totalInterest = Object.values(tagStats).reduce((sum, count) => sum + count, 0);
    
    if (totalInterest > 0) {
      // Show most significant tag type
      const topTagType = Object.entries(tagStats).reduce((max, [type, count]) => 
        count > max.count ? { type: type as TagType, count } : max,
        { type: 'interested' as TagType, count: 0 }
      );

      if (topTagType.count > 0) {
        const tagInfo = TAG_TYPES[topTagType.type];
        badges.push(
          <div
            key="interest-stats"
            className={`
              ${config.badge} bg-purple-100 text-purple-800 rounded-full font-medium
              flex items-center ${config.gap} border border-purple-200
            `}
            title={`${totalInterest} users have shown interest`}
          >
            <span className="text-lg">{tagInfo.emoji}</span>
            <span>{totalInterest}</span>
            <Users className={config.icon} />
          </div>
        );
      }
    }
  }

  // If no badges to show, return null
  if (badges.length === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap ${config.gap} ${className}`}>
      {badges}
    </div>
  );
};

// Component to show detailed interest breakdown
export const DetailedInterestBadges: React.FC<{
  userId: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}> = ({ userId, className = '', size = 'md' }) => {
  const [tagStats, setTagStats] = useState<Record<TagType, number>>({
    interested: 0,
    very_interested: 0,
    potential_match: 0
  });
  const [loading, setLoading] = useState(true);

  const sizeConfig = {
    sm: { badge: 'px-2 py-1 text-xs', gap: 'gap-1' },
    md: { badge: 'px-2.5 py-1.5 text-sm', gap: 'gap-2' },
    lg: { badge: 'px-3 py-2 text-base', gap: 'gap-3' }
  };

  const config = sizeConfig[size];

  useEffect(() => {
    loadTagStats();
  }, [userId]);

  const loadTagStats = async () => {
    setLoading(true);
    try {
      const stats = await getTagStats(userId);
      setTagStats(stats);
    } catch (err) {
      console.error('Failed to load tag stats:', err);
      // Set default values on error
      setTagStats({ interested: 0, very_interested: 0, potential_match: 0 });
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return <div className="animate-pulse">Loading...</div>;
  }

  const badges = Object.entries(tagStats)
    .filter(([_, count]) => count > 0)
    .map(([type, count]) => {
      const tagInfo = TAG_TYPES[type as TagType];
      return (
        <div
          key={type}
          className={`
            ${config.badge} bg-gradient-to-r from-purple-100 to-pink-100 
            text-purple-800 rounded-full font-medium
            flex items-center gap-1 border border-purple-200
          `}
          title={`${count} users tagged you as ${tagInfo.label}`}
        >
          <span>{tagInfo.emoji}</span>
          <span>{count}</span>
          <span className="text-xs opacity-75">{tagInfo.label}</span>
        </div>
      );
    });

  if (badges.length === 0) {
    return null;
  }

  return (
    <div className={`flex flex-wrap ${config.gap} ${className}`}>
      {badges}
    </div>
  );
};

export default UserBadges;
