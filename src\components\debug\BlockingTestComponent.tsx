import React, { useState } from 'react';
import { Shield, UserX, MessageCircle, CheckCircle, XCircle } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { 
  blockUser, 
  unblockUser, 
  isUserBlocked, 
  canUserMessage,
  getBlockedUsers 
} from '../../lib/conversationBoundaries';

const BlockingTestComponent: React.FC = () => {
  const { user } = useAuth();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [testUserId, setTestUserId] = useState('');
  const [testResults, setTestResults] = useState<string[]>([]);
  const [isRunning, setIsRunning] = useState(false);

  const addResult = (message: string, success: boolean = true) => {
    const timestamp = new Date().toLocaleTimeString();
    const icon = success ? '✅' : '❌';
    setTestResults(prev => [...prev, `${icon} [${timestamp}] ${message}`]);
  };

  const runBlockingTests = async () => {
    if (!user?.id || !testUserId) {
      showError('Please provide a test user ID');
      return;
    }

    if (user.id === testUserId) {
      showError('Cannot test blocking with your own user ID');
      return;
    }

    setIsRunning(true);
    setTestResults([]);
    const loadingToastId = showLoading('Running blocking functionality tests...');

    try {
      addResult('Starting blocking functionality tests...');

      // Test 1: Check initial block status
      addResult('Test 1: Checking initial block status...');
      const initiallyBlocked = await isUserBlocked(user.id, testUserId);
      addResult(`Initial block status: ${initiallyBlocked ? 'BLOCKED' : 'NOT BLOCKED'}`);

      // Test 2: Check if user can message initially
      addResult('Test 2: Checking initial message permissions...');
      const initialCanMessage = await canUserMessage(user.id, testUserId);
      addResult(`Can message initially: ${initialCanMessage.canMessage ? 'YES' : 'NO'} ${initialCanMessage.reason ? `(${initialCanMessage.reason})` : ''}`);

      // Test 3: Block the user
      addResult('Test 3: Attempting to block user...');
      const blockResult = await blockUser(user.id, testUserId, 'Test blocking functionality');
      addResult(`Block operation: ${blockResult.success ? 'SUCCESS' : 'FAILED'} ${blockResult.error ? `(${blockResult.error})` : ''}`, blockResult.success);

      if (blockResult.success) {
        // Test 4: Verify user is blocked
        addResult('Test 4: Verifying user is blocked...');
        const isNowBlocked = await isUserBlocked(user.id, testUserId);
        addResult(`User is now blocked: ${isNowBlocked ? 'YES' : 'NO'}`, isNowBlocked);

        // Test 5: Check message permissions after blocking
        addResult('Test 5: Checking message permissions after blocking...');
        const canMessageAfterBlock = await canUserMessage(user.id, testUserId);
        addResult(`Can message after block: ${canMessageAfterBlock.canMessage ? 'YES' : 'NO'} ${canMessageAfterBlock.reason ? `(${canMessageAfterBlock.reason})` : ''}`, !canMessageAfterBlock.canMessage);

        // Test 6: Get blocked users list
        addResult('Test 6: Checking blocked users list...');
        const blockedUsers = await getBlockedUsers(user.id);
        const isInList = blockedUsers.some(bu => bu.blocked_id === testUserId);
        addResult(`User appears in blocked list: ${isInList ? 'YES' : 'NO'}`, isInList);

        // Test 7: Unblock the user
        addResult('Test 7: Attempting to unblock user...');
        const unblockResult = await unblockUser(user.id, testUserId);
        addResult(`Unblock operation: ${unblockResult.success ? 'SUCCESS' : 'FAILED'} ${unblockResult.error ? `(${unblockResult.error})` : ''}`, unblockResult.success);

        if (unblockResult.success) {
          // Test 8: Verify user is unblocked
          addResult('Test 8: Verifying user is unblocked...');
          const isStillBlocked = await isUserBlocked(user.id, testUserId);
          addResult(`User is still blocked: ${isStillBlocked ? 'YES' : 'NO'}`, !isStillBlocked);

          // Test 9: Check message permissions after unblocking
          addResult('Test 9: Checking message permissions after unblocking...');
          const canMessageAfterUnblock = await canUserMessage(user.id, testUserId);
          addResult(`Can message after unblock: ${canMessageAfterUnblock.canMessage ? 'YES' : 'NO'} ${canMessageAfterUnblock.reason ? `(${canMessageAfterUnblock.reason})` : ''}`, canMessageAfterUnblock.canMessage);
        }
      }

      addResult('All tests completed!');
      showSuccess('Blocking functionality tests completed');

    } catch (error) {
      addResult(`Test failed with error: ${error}`, false);
      showError('Tests failed with an error');
    } finally {
      setIsRunning(false);
      dismiss(loadingToastId);
    }
  };

  const clearResults = () => {
    setTestResults([]);
  };

  if (!user?.id) {
    return (
      <div className="p-6 bg-yellow-50 border border-yellow-200 rounded-lg">
        <p className="text-yellow-800">Please log in to test blocking functionality.</p>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto p-6 bg-white rounded-lg shadow-lg">
      <div className="flex items-center mb-6">
        <Shield className="w-8 h-8 text-blue-500 mr-3" />
        <div>
          <h2 className="text-2xl font-bold text-gray-800">Blocking Functionality Test</h2>
          <p className="text-gray-600">Test the user blocking and conversation boundaries system</p>
        </div>
      </div>

      <div className="mb-6">
        <label className="block text-sm font-medium text-gray-700 mb-2">
          Test User ID (UUID of another user to test blocking with):
        </label>
        <div className="flex gap-3">
          <input
            type="text"
            value={testUserId}
            onChange={(e) => setTestUserId(e.target.value)}
            placeholder="Enter a user UUID to test with..."
            className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
          <button
            onClick={runBlockingTests}
            disabled={isRunning || !testUserId}
            className="px-6 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center gap-2"
          >
            <UserX className="w-4 h-4" />
            {isRunning ? 'Running Tests...' : 'Run Tests'}
          </button>
          <button
            onClick={clearResults}
            disabled={isRunning}
            className="px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            Clear
          </button>
        </div>
      </div>

      {testResults.length > 0 && (
        <div className="bg-gray-900 rounded-lg p-4 max-h-96 overflow-y-auto">
          <h3 className="text-green-400 font-medium mb-3">Test Results:</h3>
          <div className="space-y-1">
            {testResults.map((result, index) => (
              <div key={index} className="text-sm font-mono text-green-300">
                {result}
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
        <h4 className="font-medium text-blue-800 mb-2">What this test does:</h4>
        <ul className="text-blue-700 text-sm space-y-1">
          <li>• Tests blocking a user and verifying the block status</li>
          <li>• Checks message permissions before and after blocking</li>
          <li>• Verifies the user appears in the blocked users list</li>
          <li>• Tests unblocking and verifies permissions are restored</li>
          <li>• Validates the complete conversation boundaries integration</li>
        </ul>
      </div>
    </div>
  );
};

export default BlockingTestComponent;
