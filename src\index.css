@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    scroll-behavior: smooth;
  }
  body {
    @apply bg-slate-50 text-gray-800;
  }
}

@layer utilities {
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-in-out;
  }
  
  .animate-slideInRight {
    animation: slideInRight 0.3s ease-in-out;
  }

  @keyframes fadeIn {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }
  
  @keyframes slideInRight {
    from {
      transform: translateX(100%);
    }
    to {
      transform: translateX(0);
    }
  }

  .glass {
    background: rgba(255,255,255,0.12);
    backdrop-filter: blur(12px) saturate(180%);
    -webkit-backdrop-filter: blur(12px) saturate(180%);
    border-radius: 1.25rem;
    border: 1px solid rgba(255,255,255,0.18);
  }
  .modern-card {
    box-shadow: 0 4px 32px 0 rgba(80, 0, 120, 0.10), 0 1.5px 4px 0 rgba(0,0,0,0.04);
    border-radius: 1.25rem;
    background: white;
    transition: box-shadow 0.2s, transform 0.2s;
  }
  .modern-card:hover {
    box-shadow: 0 8px 40px 0 rgba(80, 0, 120, 0.16), 0 2px 8px 0 rgba(0,0,0,0.06);
    transform: translateY(-2px) scale(1.01);
  }
  .modern-btn {
    border-radius: 0.75rem;
    font-weight: 600;
    transition: background 0.2s, color 0.2s, box-shadow 0.2s, transform 0.2s;
    box-shadow: 0 2px 8px 0 rgba(80, 0, 120, 0.08);
  }
  .modern-btn:active {
    transform: scale(0.98);
  }
  .fade-in {
    animation: fadeIn 0.4s cubic-bezier(0.4,0,0.2,1);
  }
  .slide-up {
    animation: slideUp 0.4s cubic-bezier(0.4,0,0.2,1);
  }
  @keyframes slideUp {
    from { opacity: 0; transform: translateY(24px); }
    to { opacity: 1; transform: translateY(0); }
  }
}