import * as React from 'react';
import { createContext, useContext, useEffect, useState } from 'react';
import { Session, User } from '@supabase/supabase-js';
import { supabase } from '../../lib/supabase';


export interface AuthContextType {
  session: Session | null;
  user: User | null;
  loading: boolean;
  error: Error | null;
  systemStatus: {
    ready: boolean;
    issues: any[];
  };
}

export const AuthContext = createContext<AuthContextType>({
  session: null,
  user: null,
  loading: true,
  error: null,
  systemStatus: {
    ready: false,
    issues: []
  }
});

export const AuthProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [session, setSession] = useState<Session | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);
  const [systemStatus, setSystemStatus] = useState<{
    ready: boolean;
    issues: { component: string; error: any }[];
  }>({
    ready: false,
    issues: []
  });

  useEffect(() => {
    const initializeAuth = async () => {
      try {
        // Get initial session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession();

        if (sessionError) {
          console.error('Session error:', sessionError);
        }

        setSession(session);
        setUser(session?.user ?? null);

        // Set up auth subscription
        const { data: { subscription } } = supabase.auth.onAuthStateChange(async (event, session) => {
          console.log('Auth state change:', event, session?.user?.id);
          setSession(session);
          setUser(session?.user ?? null);
        });

        setLoading(false);
        setSystemStatus({ ready: true, issues: [] });

        return () => {
          subscription.unsubscribe();
        };
      } catch (err) {
        console.error('Auth initialization error:', err);
        setError(err instanceof Error ? err : new Error('Failed to initialize auth'));
        setSession(null);
        setUser(null);
        setLoading(false);
        setSystemStatus({ ready: false, issues: [{ component: 'auth', error: err }] });
      }
    };

    initializeAuth();
  }, []);

  return (
    <AuthContext.Provider value={{ session, user, loading, error, systemStatus }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};