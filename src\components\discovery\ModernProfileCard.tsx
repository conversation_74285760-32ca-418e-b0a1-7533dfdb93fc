import React, { useState } from 'react';
import { UserCircle, MapPin, Heart, MessageCircle, Star, Sparkles, Camera, X } from 'lucide-react';
import { Profile } from '../../types';
import UserBadges from '../tagging/UserBadges';
import { PersonalitySummary } from '../personality';

interface ModernProfileCardProps {
  profile: Profile;
  onClick?: () => void;
  onLike?: () => void;
  onPass?: () => void;
  className?: string;
  showActions?: boolean;
}

const ModernProfileCard: React.FC<ModernProfileCardProps> = ({ 
  profile, 
  onClick, 
  onLike,
  onPass,
  className = '',
  showActions = false 
}) => {
  const [isImageLoaded, setIsImageLoaded] = useState(false);
  const [currentPhotoIndex, setCurrentPhotoIndex] = useState(0);

  const truncateBio = (bio: string | undefined, maxLength: number = 100): string => {
    if (!bio) return 'Exploring life and looking for meaningful connections...';
    return bio.length > maxLength ? `${bio.substring(0, maxLength)}...` : bio;
  };

  const getAge = (profile: Profile): string => {
    if (profile.age) {
      return profile.age.toString();
    }
    return '';
  };

  const photos = profile.photos && profile.photos.length > 0 ? profile.photos : [];
  const hasPhotos = photos.length > 0;
  const currentPhoto = hasPhotos ? photos[currentPhotoIndex] : profile.avatar_url;

  const nextPhoto = () => {
    if (photos.length > 1) {
      setCurrentPhotoIndex((prev) => (prev + 1) % photos.length);
    }
  };

  const prevPhoto = () => {
    if (photos.length > 1) {
      setCurrentPhotoIndex((prev) => (prev - 1 + photos.length) % photos.length);
    }
  };

  return (
    <div className={`card group cursor-pointer transform transition-all duration-300 hover:scale-[1.02] max-w-sm mx-auto ${className}`}>
      {/* Photo Section */}
      <div className="relative h-80 overflow-hidden rounded-t-xl" onClick={onClick}>
        {currentPhoto ? (
          <>
            <img 
              src={currentPhoto} 
              alt={`${profile.name}'s photo`}
              className={`w-full h-full object-cover transition-opacity duration-300 ${
                isImageLoaded ? 'opacity-100' : 'opacity-0'
              }`}
              onLoad={() => setIsImageLoaded(true)}
            />
            {!isImageLoaded && (
              <div className="absolute inset-0 bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
                <div className="animate-pulse">
                  <UserCircle className="w-16 h-16 text-purple-300" />
                </div>
              </div>
            )}
          </>
        ) : (
          <div className="w-full h-full bg-gradient-to-br from-purple-100 to-pink-100 flex items-center justify-center">
            <UserCircle className="w-20 h-20 text-purple-300" />
          </div>
        )}

        {/* Photo Navigation */}
        {photos.length > 1 && (
          <>
            <button
              onClick={(e) => { e.stopPropagation(); prevPhoto(); }}
              className="absolute left-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white transition-all duration-200 opacity-0 group-hover:opacity-100"
            >
              ‹
            </button>
            <button
              onClick={(e) => { e.stopPropagation(); nextPhoto(); }}
              className="absolute right-2 top-1/2 transform -translate-y-1/2 w-8 h-8 bg-black/20 hover:bg-black/40 rounded-full flex items-center justify-center text-white transition-all duration-200 opacity-0 group-hover:opacity-100"
            >
              ›
            </button>
            
            {/* Photo Indicators */}
            <div className="absolute top-4 left-1/2 transform -translate-x-1/2 flex space-x-1">
              {photos.map((_, index) => (
                <div
                  key={index}
                  className={`w-2 h-2 rounded-full transition-all duration-200 ${
                    index === currentPhotoIndex ? 'bg-white' : 'bg-white/50'
                  }`}
                />
              ))}
            </div>
          </>
        )}

        {/* Gradient Overlay */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent" />

        {/* Name and Age Overlay */}
        <div className="absolute bottom-4 left-4 text-white">
          <h3 className="text-2xl font-bold mb-1">
            {profile.name}
            {getAge(profile) && (
              <span className="text-xl font-normal ml-2">{getAge(profile)}</span>
            )}
          </h3>
          {profile.location && (
            <div className="flex items-center text-sm text-white/90">
              <MapPin className="w-4 h-4 mr-1" />
              {profile.location}
            </div>
          )}
        </div>

        {/* Online Status */}
        <div className="absolute top-4 right-4">
          <div className="w-3 h-3 bg-green-400 rounded-full border-2 border-white shadow-lg"></div>
        </div>
      </div>

      {/* Content Section */}
      <div className="p-4 space-y-4">
        {/* Bio */}
        <p className="text-gray-600 text-sm leading-relaxed">
          {truncateBio(profile.bio)}
        </p>

        {/* Personality Summary */}
        <PersonalitySummary
          scores={{
            extraversion_score: profile.extraversion_score,
            agreeableness_score: profile.agreeableness_score,
            conscientiousness_score: profile.conscientiousness_score,
            neuroticism_score: profile.neuroticism_score,
            openness_score: profile.openness_score,
            personality_insights: profile.personality_insights
          }}
          variant="compact"
          showInsights={false}
        />

        {/* User Badges */}
        <UserBadges
          userId={profile.id}
          size="sm"
          className="flex-wrap"
        />
      </div>

      {/* Action Buttons */}
      {showActions && (
        <div className="flex items-center justify-center space-x-4 p-4 border-t border-gray-100">
          <button
            onClick={(e) => { e.stopPropagation(); onPass?.(); }}
            className="w-12 h-12 rounded-full bg-gray-100 hover:bg-gray-200 flex items-center justify-center transition-all duration-200 hover:scale-110"
          >
            <X className="w-6 h-6 text-gray-600" />
          </button>
          <button
            onClick={(e) => { e.stopPropagation(); onClick?.(); }}
            className="w-12 h-12 rounded-full bg-blue-100 hover:bg-blue-200 flex items-center justify-center transition-all duration-200 hover:scale-110"
          >
            <MessageCircle className="w-6 h-6 text-blue-600" />
          </button>
          <button
            onClick={(e) => { e.stopPropagation(); onLike?.(); }}
            className="w-12 h-12 rounded-full bg-gradient-to-r from-pink-500 to-red-500 hover:from-pink-600 hover:to-red-600 flex items-center justify-center transition-all duration-200 hover:scale-110 shadow-lg"
          >
            <Heart className="w-6 h-6 text-white" />
          </button>
        </div>
      )}
    </div>
  );
};

export default ModernProfileCard;
