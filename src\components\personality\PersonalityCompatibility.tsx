import React from 'react';
import { Heart, TrendingUp, Users, AlertCircle } from 'lucide-react';

interface PersonalityScores {
  extraversion_score?: number;
  agreeableness_score?: number;
  conscientiousness_score?: number;
  neuroticism_score?: number;
  openness_score?: number;
}

interface PersonalityCompatibilityProps {
  userScores: PersonalityScores;
  otherUserScores: PersonalityScores;
  userName?: string;
  otherUserName?: string;
  showDetails?: boolean;
}

const PersonalityCompatibility: React.FC<PersonalityCompatibilityProps> = ({
  userScores,
  otherUserScores,
  userName = 'You',
  otherUserName = 'Other User',
  showDetails = false
}) => {
  const traits = [
    { name: 'Extraversion', key: 'extraversion_score', icon: Users },
    { name: 'Agreeableness', key: 'agreeableness_score', icon: Heart },
    { name: 'Conscientiousness', key: 'conscientiousness_score', icon: TrendingUp },
    { name: 'Neuroticism', key: 'neuroticism_score', icon: AlertCircle },
    { name: 'Openness', key: 'openness_score', icon: TrendingUp }
  ];

  const calculateCompatibility = () => {
    let totalDifference = 0;
    let validTraits = 0;

    traits.forEach(trait => {
      const userScore = userScores[trait.key as keyof PersonalityScores] || 0;
      const otherScore = otherUserScores[trait.key as keyof PersonalityScores] || 0;
      
      if (userScore > 0 && otherScore > 0) {
        totalDifference += Math.abs(userScore - otherScore);
        validTraits++;
      }
    });

    if (validTraits === 0) return 0;

    // Convert difference to compatibility score (0-100)
    const averageDifference = totalDifference / validTraits;
    const compatibility = Math.max(0, 100 - averageDifference);
    return Math.round(compatibility);
  };

  const getCompatibilityLevel = (score: number) => {
    if (score >= 85) return { level: 'Excellent', color: 'text-green-600 bg-green-100', description: 'Very high compatibility' };
    if (score >= 70) return { level: 'Good', color: 'text-blue-600 bg-blue-100', description: 'Good compatibility' };
    if (score >= 55) return { level: 'Moderate', color: 'text-yellow-600 bg-yellow-100', description: 'Moderate compatibility' };
    if (score >= 40) return { level: 'Fair', color: 'text-orange-600 bg-orange-100', description: 'Some differences' };
    return { level: 'Low', color: 'text-red-600 bg-red-100', description: 'Significant differences' };
  };

  const getTraitCompatibility = (trait: any) => {
    const userScore = userScores[trait.key as keyof PersonalityScores] || 0;
    const otherScore = otherUserScores[trait.key as keyof PersonalityScores] || 0;
    
    if (userScore === 0 || otherScore === 0) return null;
    
    const difference = Math.abs(userScore - otherScore);
    const compatibility = Math.max(0, 100 - difference);
    
    return {
      userScore,
      otherScore,
      difference,
      compatibility: Math.round(compatibility)
    };
  };

  const overallCompatibility = calculateCompatibility();
  const compatibilityInfo = getCompatibilityLevel(overallCompatibility);

  const hasValidData = traits.some(trait => {
    const userScore = userScores[trait.key as keyof PersonalityScores] || 0;
    const otherScore = otherUserScores[trait.key as keyof PersonalityScores] || 0;
    return userScore > 0 && otherScore > 0;
  });

  if (!hasValidData) {
    return (
      <div className="bg-gray-50 rounded-lg p-4 text-center">
        <AlertCircle className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500 text-sm">
          Personality compatibility requires both users to complete the assessment
        </p>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-lg border p-4 space-y-4">
      {/* Overall Compatibility Score */}
      <div className="text-center">
        <div className="flex items-center justify-center mb-2">
          <Heart className="w-5 h-5 text-purple-600 mr-2" />
          <h3 className="text-lg font-semibold text-gray-800">
            Personality Compatibility
          </h3>
        </div>
        
        <div className={`inline-flex items-center px-4 py-2 rounded-full ${compatibilityInfo.color}`}>
          <span className="text-2xl font-bold mr-2">{overallCompatibility}%</span>
          <span className="font-medium">{compatibilityInfo.level}</span>
        </div>
        
        <p className="text-sm text-gray-600 mt-2">
          {compatibilityInfo.description}
        </p>
      </div>

      {/* Detailed Breakdown */}
      {showDetails && (
        <div className="space-y-3">
          <h4 className="text-sm font-semibold text-gray-700 border-b pb-1">
            Trait Comparison
          </h4>
          
          {traits.map(trait => {
            const traitData = getTraitCompatibility(trait);
            const IconComponent = trait.icon;
            
            if (!traitData) return null;
            
            return (
              <div key={trait.key} className="space-y-2">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <IconComponent className="w-4 h-4 text-gray-600 mr-2" />
                    <span className="text-sm font-medium text-gray-700">
                      {trait.name}
                    </span>
                  </div>
                  <span className="text-sm font-bold text-gray-800">
                    {traitData.compatibility}%
                  </span>
                </div>
                
                <div className="flex items-center space-x-2 text-xs">
                  <div className="flex-1">
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-600">{userName}</span>
                      <span className="font-medium">{traitData.userScore}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${traitData.userScore}%` }}
                      />
                    </div>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex justify-between mb-1">
                      <span className="text-gray-600">{otherUserName}</span>
                      <span className="font-medium">{traitData.otherScore}</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-purple-500 h-2 rounded-full"
                        style={{ width: `${traitData.otherScore}%` }}
                      />
                    </div>
                  </div>
                </div>
              </div>
            );
          })}
        </div>
      )}

      {/* Compatibility Insights */}
      <div className="bg-purple-50 rounded-lg p-3">
        <h4 className="text-sm font-semibold text-purple-800 mb-2">
          Compatibility Insights
        </h4>
        <div className="text-sm text-purple-700 space-y-1">
          {overallCompatibility >= 70 && (
            <p>• You share similar personality traits that could lead to good understanding</p>
          )}
          {overallCompatibility >= 55 && overallCompatibility < 70 && (
            <p>• You have some personality differences that could complement each other</p>
          )}
          {overallCompatibility < 55 && (
            <p>• You have different personality styles that might require more understanding</p>
          )}
          <p>• Remember: differences can also create interesting dynamics and growth opportunities</p>
        </div>
      </div>
    </div>
  );
};

export default PersonalityCompatibility;
