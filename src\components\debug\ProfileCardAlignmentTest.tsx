import React from 'react';
import { Profile } from '../../types';
import ProfileCard from '../discovery/ProfileCard';
import styles from '../../pages/Discover.module.css';

/**
 * Test component to verify ProfileCard alignment with varying content lengths
 */
const ProfileCardAlignmentTest: React.FC = () => {
  // Test profiles with varying content lengths
  const testProfiles: Profile[] = [
    {
      id: 'test-1',
      name: '<PERSON>',
      age: 28,
      gender: 'Female',
      bio: 'Short bio.',
      interests: ['Reading', 'Yoga'],
      avatar_url: '',
      username: 'alice_j',
      full_name: '<PERSON>',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'test-2',
      name: '<PERSON>',
      age: 35,
      gender: 'Male',
      bio: 'This is a much longer biography that should test how the card handles overflow text. It contains multiple sentences and should demonstrate the text truncation and consistent height behavior. The bio continues with more details about interests, hobbies, and personal philosophy to really test the limits.',
      interests: ['Photography', 'Travel', 'Cooking', 'Hiking', 'Music', 'Art', 'Technology'],
      avatar_url: '',
      username: 'rob_williams',
      full_name: '<PERSON>',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'test-3',
      name: 'Sam',
      age: 22,
      bio: 'Medium length bio that spans a couple of lines and provides some insight into personality and interests without being too overwhelming.',
      interests: ['Gaming'],
      avatar_url: '',
      username: 'sam_22',
      full_name: 'Sam',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'test-4',
      name: 'Dr. Elizabeth Margaret Thompson-Smith',
      gender: 'Female',
      bio: '',
      interests: [],
      avatar_url: '',
      username: 'dr_thompson_smith',
      full_name: 'Dr. Elizabeth Margaret Thompson-Smith',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'test-5',
      name: 'Mike Chen',
      age: 31,
      gender: 'Male',
      bio: 'Passionate about technology and innovation. Love exploring new places and meeting interesting people. Always up for a good conversation about science, philosophy, or the latest tech trends.',
      interests: ['Technology', 'Science', 'Philosophy'],
      avatar_url: '',
      username: 'mike_chen',
      full_name: 'Mike Chen',
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: 'test-6',
      name: 'Luna',
      age: 26,
      gender: 'Non-binary',
      bio: 'Artist and dreamer. Creating beautiful things and spreading positive vibes. Life is an adventure!',
      interests: ['Art', 'Music', 'Dancing', 'Meditation', 'Nature', 'Spirituality', 'Creativity', 'Wellness', 'Community'],
      avatar_url: '',
      username: 'luna_artist',
      full_name: 'Luna',
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  return (
    <div className="max-w-7xl mx-auto p-6">
      <div className="mb-8">
        <h1 className="text-3xl font-bold text-gray-800 mb-4">
          Profile Card Alignment Test
        </h1>
        <p className="text-gray-600 mb-4">
          This test verifies that profile cards maintain consistent alignment and layout 
          regardless of content length variations.
        </p>
        
        <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6">
          <h3 className="font-semibold text-blue-800 mb-2">Test Scenarios:</h3>
          <ul className="text-blue-700 text-sm space-y-1">
            <li>• <strong>Card 1:</strong> Short bio, few interests</li>
            <li>• <strong>Card 2:</strong> Very long bio, many interests, long name</li>
            <li>• <strong>Card 3:</strong> Medium bio, single interest</li>
            <li>• <strong>Card 4:</strong> No bio, no interests, very long name</li>
            <li>• <strong>Card 5:</strong> Medium bio, moderate interests</li>
            <li>• <strong>Card 6:</strong> Short bio, many interests</li>
          </ul>
        </div>
      </div>

      {/* Test Grid */}
      <div className={styles.profileGrid}>
        {testProfiles.map((profile) => (
          <div key={profile.id} className={styles.profileCardWrapper}>
            <ProfileCard
              profile={profile}
              onClick={() => console.log(`Clicked profile: ${profile.name}`)}
            />
          </div>
        ))}
      </div>

      {/* Alignment Guidelines */}
      <div className="mt-8 p-6 bg-gray-50 rounded-lg">
        <h3 className="font-semibold text-gray-800 mb-4">Alignment Verification Checklist:</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Visual Consistency:</h4>
            <ul className="space-y-1 text-gray-600">
              <li>✓ All cards have the same height</li>
              <li>✓ Avatar sections are aligned horizontally</li>
              <li>✓ Name titles are at the same baseline</li>
              <li>✓ Bio sections have consistent height</li>
              <li>✓ Interest tags start at the same position</li>
            </ul>
          </div>
          <div>
            <h4 className="font-medium text-gray-700 mb-2">Content Handling:</h4>
            <ul className="space-y-1 text-gray-600">
              <li>✓ Long bios are properly truncated</li>
              <li>✓ Long names are truncated with ellipsis</li>
              <li>✓ Missing content doesn't break layout</li>
              <li>✓ Many interests are limited and show "+X more"</li>
              <li>✓ Empty sections maintain spacing</li>
            </ul>
          </div>
        </div>
      </div>

      {/* Responsive Test Instructions */}
      <div className="mt-6 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
        <h4 className="font-medium text-yellow-800 mb-2">Responsive Testing:</h4>
        <p className="text-yellow-700 text-sm">
          Resize your browser window to test the responsive grid behavior. 
          Cards should maintain alignment at all screen sizes: mobile (1 column), 
          tablet (2 columns), and desktop (3 columns).
        </p>
      </div>
    </div>
  );
};

export default ProfileCardAlignmentTest;
