#!/usr/bin/env node

/**
 * Validation script for personality system integration
 * Validates that all personality components and features are working correctly
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';

// Read environment variables
let supabaseUrl, supabaseKey;
try {
  const envContent = readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n');
  
  for (const line of envLines) {
    if (line.startsWith('VITE_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].trim();
    }
    if (line.startsWith('VITE_SUPABASE_ANON_KEY=')) {
      supabaseKey = line.split('=')[1].trim();
    }
  }
} catch (error) {
  supabaseUrl = process.env.VITE_SUPABASE_URL;
  supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function validatePersonalityIntegration() {
  console.log('🔍 Validating Personality System Integration...\n');

  let allTestsPassed = true;

  try {
    // Test 1: Validate database schema completeness
    console.log('1️⃣ Validating database schema...');
    const { data: schemaData, error: schemaError } = await supabase
      .from('profiles')
      .select(`
        id,
        extraversion_score,
        agreeableness_score,
        conscientiousness_score,
        neuroticism_score,
        openness_score,
        personality_test_completed_at,
        personality_insights
      `)
      .limit(1);

    if (schemaError) {
      console.error('❌ Schema validation failed:', schemaError.message);
      allTestsPassed = false;
    } else {
      console.log('✅ All personality columns exist in database');
    }

    // Test 2: Validate personality data structure
    console.log('\n2️⃣ Validating personality data structure...');
    const { data: profilesWithPersonality } = await supabase
      .from('profiles')
      .select('*')
      .not('personality_test_completed_at', 'is', null)
      .limit(5);

    if (profilesWithPersonality && profilesWithPersonality.length > 0) {
      console.log(`✅ Found ${profilesWithPersonality.length} profiles with personality data`);
      
      // Validate data structure
      const profile = profilesWithPersonality[0];
      const requiredFields = ['extraversion_score', 'agreeableness_score', 'conscientiousness_score', 'neuroticism_score', 'openness_score'];
      const missingFields = requiredFields.filter(field => profile[field] === null || profile[field] === undefined);
      
      if (missingFields.length > 0) {
        console.error('❌ Missing personality scores:', missingFields);
        allTestsPassed = false;
      } else {
        console.log('✅ Personality scores structure is valid');
      }

      // Validate insights structure
      if (profile.personality_insights) {
        const insights = profile.personality_insights;
        const expectedInsightFields = ['overallProfile', 'strengths', 'bigFiveScores'];
        const hasValidInsights = expectedInsightFields.some(field => insights[field]);
        
        if (hasValidInsights) {
          console.log('✅ Personality insights structure is valid');
        } else {
          console.log('⚠️ Personality insights structure could be improved');
        }
      }
    } else {
      console.log('⚠️ No profiles with personality data found - this is expected for new installations');
    }

    // Test 3: Validate personality compatibility calculation
    console.log('\n3️⃣ Validating personality compatibility calculation...');
    
    // Test with sample data
    const testUser1 = {
      extraversion_score: 75,
      agreeableness_score: 80,
      conscientiousness_score: 70,
      neuroticism_score: 30,
      openness_score: 85
    };

    const testUser2 = {
      extraversion_score: 65,
      agreeableness_score: 85,
      conscientiousness_score: 75,
      neuroticism_score: 25,
      openness_score: 80
    };

    // Calculate compatibility
    const traits = ['extraversion_score', 'agreeableness_score', 'conscientiousness_score', 'neuroticism_score', 'openness_score'];
    let totalDifference = 0;
    let validTraits = 0;

    traits.forEach(trait => {
      const score1 = testUser1[trait] || 0;
      const score2 = testUser2[trait] || 0;
      
      if (score1 > 0 && score2 > 0) {
        totalDifference += Math.abs(score1 - score2);
        validTraits++;
      }
    });

    const compatibility = validTraits > 0 ? Math.max(0, 100 - (totalDifference / validTraits)) : 0;
    
    if (compatibility > 0 && compatibility <= 100) {
      console.log(`✅ Compatibility calculation working: ${Math.round(compatibility)}%`);
    } else {
      console.error('❌ Compatibility calculation failed');
      allTestsPassed = false;
    }

    // Test 4: Validate personality filtering logic
    console.log('\n4️⃣ Validating personality filtering logic...');
    
    const { data: allProfiles } = await supabase
      .from('profiles')
      .select('id, extraversion_score, agreeableness_score, conscientiousness_score, neuroticism_score, openness_score')
      .not('extraversion_score', 'is', null)
      .limit(10);

    if (allProfiles && allProfiles.length > 0) {
      // Test filtering by high compatibility
      const userScores = testUser1;
      const highCompatibilityProfiles = allProfiles.filter(profile => {
        let totalDiff = 0;
        let validTraitCount = 0;

        traits.forEach(trait => {
          const userScore = userScores[trait] || 0;
          const profileScore = profile[trait] || 0;
          
          if (userScore > 0 && profileScore > 0) {
            totalDiff += Math.abs(userScore - profileScore);
            validTraitCount++;
          }
        });

        const compat = validTraitCount > 0 ? Math.max(0, 100 - (totalDiff / validTraitCount)) : 0;
        return compat >= 70; // High compatibility threshold
      });

      console.log(`✅ Personality filtering working: ${highCompatibilityProfiles.length}/${allProfiles.length} profiles meet high compatibility criteria`);
    } else {
      console.log('⚠️ No profiles with personality data available for filtering test');
    }

    // Test 5: Validate component integration points
    console.log('\n5️⃣ Validating component integration points...');
    
    // Check if personality visualization components exist
    const componentPaths = [
      'src/components/personality/PersonalityVisualization.tsx',
      'src/components/personality/PersonalityRadarChart.tsx',
      'src/components/personality/PersonalitySummary.tsx',
      'src/components/personality/PersonalityCompatibility.tsx',
      'src/components/personality/index.ts'
    ];

    let componentsExist = true;
    for (const path of componentPaths) {
      try {
        readFileSync(path, 'utf8');
      } catch (error) {
        console.error(`❌ Component missing: ${path}`);
        componentsExist = false;
        allTestsPassed = false;
      }
    }

    if (componentsExist) {
      console.log('✅ All personality visualization components exist');
    }

    // Test 6: Validate route configuration
    console.log('\n6️⃣ Validating route configuration...');
    
    try {
      const appContent = readFileSync('src/App.tsx', 'utf8');
      const hasPersonalityRoute = appContent.includes('/personality-test') && appContent.includes('PersonalityTestPage');
      
      if (hasPersonalityRoute) {
        console.log('✅ Personality test route is properly configured');
      } else {
        console.error('❌ Personality test route is missing or misconfigured');
        allTestsPassed = false;
      }
    } catch (error) {
      console.error('❌ Could not validate route configuration:', error.message);
      allTestsPassed = false;
    }

    // Test 7: Validate integration in main components
    console.log('\n7️⃣ Validating integration in main components...');
    
    const integrationFiles = [
      { file: 'src/components/discovery/DetailedProfileView.tsx', component: 'PersonalityVisualization' },
      { file: 'src/components/discovery/ProfileCard.tsx', component: 'PersonalitySummary' },
      { file: 'src/pages/Settings.tsx', component: 'PersonalityVisualization' },
      { file: 'src/pages/Discover.tsx', component: 'personalityCompatibility' }
    ];

    let integrationComplete = true;
    for (const { file, component } of integrationFiles) {
      try {
        const content = readFileSync(file, 'utf8');
        if (content.includes(component)) {
          console.log(`✅ ${component} integrated in ${file.split('/').pop()}`);
        } else {
          console.log(`⚠️ ${component} not found in ${file.split('/').pop()}`);
        }
      } catch (error) {
        console.error(`❌ Could not check integration in ${file}`);
        integrationComplete = false;
      }
    }

    if (integrationComplete) {
      console.log('✅ Component integration validation completed');
    }

    console.log('\n' + '='.repeat(50));
    if (allTestsPassed) {
      console.log('🎉 All personality system validations passed!');
      console.log('✅ The personality system is fully integrated and working correctly.');
    } else {
      console.log('⚠️ Some validations failed. Please review the issues above.');
    }

    return allTestsPassed;

  } catch (error) {
    console.error('❌ Validation failed with error:', error.message);
    return false;
  }
}

// Run the validation
validatePersonalityIntegration()
  .then(success => {
    process.exit(success ? 0 : 1);
  })
  .catch(error => {
    console.error('❌ Validation script failed:', error);
    process.exit(1);
  });
