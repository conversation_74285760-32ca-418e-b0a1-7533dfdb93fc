# SoulTwinSync - AI-Powered Personality Matching Platform

A sophisticated React + TypeScript application that creates digital twins for users based on personality assessments and enables meaningful connections through AI-powered matching.

## 🚀 Quick Start

### 1. Database Setup
```bash
# Apply the main database fix in Supabase SQL Editor
database/fixes/FINAL_WORKING_FIX.sql

# Or use the automated script
npm run db:migrate
```

### 2. Environment Configuration
```env
VITE_SUPABASE_URL=<your project url>
VITE_SUPABASE_ANON_KEY=<your anon key>
VITE_AVATARS_BUCKET=avatars  # Optional if your bucket is not named "avatars"
```

### 3. Start Development
```bash
# Use the provided batch script
npm run dev:start

# Or manually
npm install
npm run dev
```

## 📁 Project Structure

```
SoulTwinSync/
├── database/                    # Database-related files
│   ├── migrations/             # Schema changes and migrations
│   ├── seeds/                  # Test data and sample records
│   └── fixes/                  # Repair and fix scripts
├── docs/                       # All documentation
│   ├── setup/                  # Installation and configuration
│   ├── api/                    # API documentation
│   ├── database/               # Database schema and migration docs
│   └── features/               # Feature-specific documentation
├── tests/                      # Testing files
│   ├── unit/                   # Component and function tests
│   ├── integration/            # API and database integration tests
│   ├── e2e/                    # End-to-end user flow tests
│   └── fixtures/               # Test data and mock files
├── scripts/                    # Development and deployment scripts
│   ├── database/               # Database management scripts
│   ├── deployment/             # Build and deployment scripts
│   └── utilities/              # Development helper scripts
├── src/                        # Application source code
├── public/                     # Static assets
└── supabase/                   # Supabase configuration
```

## ✨ Key Features

- **🧠 Personality Assessment** - Big Five personality test (mandatory for digital twin creation)
- **🤖 Digital Twin System** - AI-powered personality modeling for better matches
- **🔍 Smart Discovery** - Personality-based user discovery with filtering
- **💬 Conversation System** - Real-time messaging between matched users  
- **🏷️ Interest Tagging** - "Very Interested" and "Willing to Contact" features
- **🔐 Authentication** - Secure Supabase auth with profile management
- **📱 Responsive Design** - Modern UI with Tailwind CSS

## 🎯 Core Functionality

1. **User Registration & Profile Creation**
2. **Mandatory Personality Test** - Required for digital twin functionality
3. **Personality-Based Discovery** - Find compatible matches
4. **Interest Expression** - Tag users you're interested in
5. **Real-time Conversations** - Chat with matched users

## 🛠️ Development

### Prerequisites
- Node.js 16+
- Supabase account and project
- Environment variables configured

### Setup Storage
Create a public storage bucket for user avatars in Supabase and ensure it allows uploads with your anon key. Avatar images will be stored in this bucket and referenced from the `profiles.avatar_url` column.

### Database Requirements
- Run `database/fixes/FINAL_WORKING_FIX.sql` for complete setup
- Creates all required tables and RLS policies
- Fixes 403 Forbidden errors
- Adds personality test fields

### Testing
```bash
# Test database functionality
npm run test:integration

# Verify discovery system
npm run test:discovery

# Check tagging system
npm run test:tagging

# Check RLS policies
npm run test:rls
```

## 📚 Documentation

- **Project Structure**: `/docs/setup/PROJECT_STRUCTURE_MIGRATION.md`
- **Database Setup**: `/docs/database/DATABASE_FIX_INSTRUCTIONS.md`
- **Conversation Boundaries**: `/docs/features/CONVERSATION_BOUNDARIES_README.md`
- **Tagging System**: `/docs/features/TAGGING_SYSTEM_SETUP.md`
- **Scripts Guide**: `/scripts/README.md`

## 🔧 Troubleshooting

### Common Issues:
1. **403 Forbidden Errors** - Run `npm run db:migrate`
2. **Personality Test Not Loading** - Check database personality columns
3. **Discovery Not Working** - Verify RLS policies are applied
4. **Tagging Fails** - Check `user_interest_tags` table exists

### Verification Tools:
```bash
# Check database state
npm run db:check

# Verify RLS policies
npm run test:rls

# Diagnose issues
npm run verify

# Debug database users
npm run db:debug
```

## 🎨 Tech Stack

- **Frontend**: React 18, TypeScript, Tailwind CSS
- **Backend**: Supabase (PostgreSQL, Auth, Storage)
- **Build Tool**: Vite
- **Testing**: Jest
- **Routing**: React Router v6
- **State Management**: Context API + Zustand

---

**Ready to create meaningful connections through personality-based matching!** ✨
