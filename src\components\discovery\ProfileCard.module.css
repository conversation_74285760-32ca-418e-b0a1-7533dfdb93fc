/* ProfileCard.module.css - Dedicated styles for consistent profile card layout */

.profileCard {
  @apply bg-white rounded-2xl shadow-xl overflow-hidden transform transition-all duration-300 hover:shadow-2xl hover:-translate-y-2 hover:scale-[1.02] cursor-pointer;
  display: flex;
  flex-direction: column;
  height: 100%;
  min-height: 500px; /* Ensures consistent minimum height */
  max-width: 340px;
  margin: 0 auto;
  border: 1px solid rgba(139, 92, 246, 0.1);
}

.avatarSection {
  @apply bg-gradient-to-br from-purple-500 via-purple-600 to-pink-500 flex items-center justify-center relative;
  height: 180px; /* Fixed height for consistency */
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.avatarSection::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: radial-gradient(circle at 30% 30%, rgba(255, 255, 255, 0.2) 0%, transparent 50%);
}

.avatar {
  @apply w-28 h-28 rounded-full object-cover border-4 border-white shadow-xl;
  position: relative;
  z-index: 2;
}

.avatarPlaceholder {
  @apply w-28 h-28 rounded-full bg-white/20 flex items-center justify-center border-4 border-white shadow-xl backdrop-blur-sm;
  position: relative;
  z-index: 2;
}

.contentSection {
  @apply p-6;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  min-height: 320px; /* Ensures consistent content area height */
}

.headerSection {
  @apply mb-4;
  flex-shrink: 0;
}

.profileName {
  @apply text-xl font-bold text-gray-800 mb-1 leading-tight;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  max-width: 100%;
}

.profileDetails {
  height: 20px; /* Fixed height for age/gender line */
  display: flex;
  align-items: center;
}

.profileDetailsText {
  @apply text-sm text-gray-600;
}

.bioSection {
  @apply mb-4;
  flex-shrink: 0;
}

.bioContainer {
  height: 64px; /* Fixed height for bio - 4 lines at 16px line height */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 4;
  -webkit-box-orient: vertical;
  position: relative;
}

.bioContainer::after {
  content: '';
  position: absolute;
  bottom: 0;
  right: 0;
  width: 20px;
  height: 16px;
  background: linear-gradient(to right, transparent, white);
  pointer-events: none;
}

.bioText {
  @apply text-gray-700 text-sm leading-relaxed;
}

.badgesSection {
  @apply mb-4;
  flex-shrink: 0;
}

.badgesContainer {
  height: 32px; /* Fixed height for badges */
  display: flex;
  align-items: flex-start;
}

.interestsSection {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  justify-content: flex-start;
  min-height: 80px; /* Minimum height for interests section */
}

.interestsHeader {
  @apply text-xs font-semibold text-gray-500 uppercase mb-2 tracking-wide;
}

.interestsContainer {
  @apply flex flex-wrap gap-2;
}

.interestTag {
  @apply px-3 py-1 text-xs bg-purple-100 text-purple-700 rounded-full font-medium;
  white-space: nowrap;
}

.moreInterestsTag {
  @apply px-3 py-1 text-xs bg-gray-100 text-gray-700 rounded-full font-medium;
  white-space: nowrap;
}

.noInterests {
  @apply text-xs text-gray-400 italic;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .profileCard {
    min-height: 440px;
  }
  
  .contentSection {
    min-height: 280px;
  }
  
  .bioContainer {
    height: 48px; /* Slightly smaller on mobile */
  }
}

/* Ensure consistent grid alignment */
.profileCard:nth-child(n) {
  align-self: stretch;
}

/* Hover effects */
.profileCard:hover .avatar {
  @apply scale-105;
  transition: transform 0.3s ease;
}

.profileCard:hover .profileName {
  @apply text-purple-700;
  transition: color 0.3s ease;
}

/* Focus states for accessibility */
.profileCard:focus {
  @apply outline-none ring-2 ring-purple-500 ring-offset-2;
}

.profileCard:focus-visible {
  @apply outline-none ring-2 ring-purple-500 ring-offset-2;
}
