# Scripts Directory

This directory contains all utility scripts, database management files, and testing utilities for the SoulTwinSync project.

## 📁 Directory Structure

### `/database/` - Database Scripts
SQL scripts for database setup, migration, and maintenance:

- **`FINAL_WORKING_FIX.sql`** - ⭐ **MAIN DATABASE SETUP SCRIPT** 
  - Complete database setup and fix script
  - Creates missing tables and columns
  - Fixes RLS policies for 403 Forbidden errors
  - **Run this first in Supabase SQL Editor**

- **Other SQL files** - Additional database fixes and migrations
  - Various specialized fix scripts
  - Schema creation scripts
  - Test data scripts

### `/tests/` - Test Scripts
JavaScript test files for verifying functionality:

- Database connection tests
- User discovery tests  
- Tagging system tests
- End-to-end functionality verification

### `/utilities/` - Utility Scripts
JavaScript/Node.js utilities for maintenance and diagnostics:

- **`.mjs` files** - ES module utilities for database management
- **`.js` files** - Diagnostic and verification scripts
- Database state checking tools
- Data management utilities

## 🚀 Quick Start

1. **First Time Setup**:
   ```bash
   # Run this in Supabase SQL Editor
   database/fixes/FINAL_WORKING_FIX.sql

   # Or use the automated script
   npm run db:migrate
   ```

2. **Test Everything Works**:
   ```bash
   npm run test:integration
   ```

3. **Check Database State**:
   ```bash
   npm run db:check
   ```

## ⚠️ Important Notes

- Always backup your database before running SQL scripts
- Run `FINAL_WORKING_FIX.sql` first for complete setup
- Test scripts require valid environment variables
- Utility scripts may need proper Supabase credentials

## 📝 Script Usage

Most scripts can be run via npm scripts (recommended):
```bash
# Database operations
npm run db:migrate      # Apply all database fixes
npm run db:fix          # Apply core fixes
npm run db:check        # Check database state

# Testing
npm run test:integration # Run integration tests
npm run verify          # Run verification checklist

# Utilities
npm run db:users        # Check database users
npm run db:debug        # Debug user issues
```

Or directly with Node.js:
```bash
node scripts/database/script-name.mjs
node tests/integration/test-name.js
```

SQL scripts should be run in Supabase SQL Editor for proper execution context.

## 📁 New Organization

**Note**: This directory has been reorganized for better maintainability:
- **Database scripts** → `database/` subdirectory
- **Test scripts** → `../tests/integration/` directory
- **Utility scripts** → `utilities/` subdirectory
- **Deployment scripts** → `deployment/` subdirectory

See `../docs/setup/PROJECT_STRUCTURE_MIGRATION.md` for complete migration details.
