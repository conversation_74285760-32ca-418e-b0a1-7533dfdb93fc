import React from 'react';
import { Brain, TrendingUp, Users, Target, Heart, Zap } from 'lucide-react';

interface PersonalityScores {
  extraversion_score?: number;
  agreeableness_score?: number;
  conscientiousness_score?: number;
  neuroticism_score?: number;
  openness_score?: number;
}

interface PersonalityVisualizationProps {
  scores: PersonalityScores;
  insights?: any;
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
  showInsights?: boolean;
}

const PersonalityVisualization: React.FC<PersonalityVisualizationProps> = ({
  scores,
  insights,
  size = 'md',
  showLabels = true,
  showInsights = false
}) => {
  const traits = [
    {
      name: 'Extraversion',
      key: 'extraversion_score',
      score: scores.extraversion_score || 0,
      gradient: 'from-blue-500 to-blue-600',
      lightGradient: 'from-blue-50 to-blue-100',
      icon: Users,
      description: 'Social energy and assertiveness',
      color: '#3B82F6'
    },
    {
      name: 'Agreeableness',
      key: 'agreeableness_score',
      score: scores.agreeableness_score || 0,
      gradient: 'from-green-500 to-emerald-600',
      lightGradient: 'from-green-50 to-emerald-100',
      icon: Heart,
      description: 'Cooperation and trust',
      color: '#10B981'
    },
    {
      name: 'Conscientiousness',
      key: 'conscientiousness_score',
      score: scores.conscientiousness_score || 0,
      gradient: 'from-purple-500 to-purple-600',
      lightGradient: 'from-purple-50 to-purple-100',
      icon: Target,
      description: 'Organization and discipline',
      color: '#8B5CF6'
    },
    {
      name: 'Neuroticism',
      key: 'neuroticism_score',
      score: scores.neuroticism_score || 0,
      gradient: 'from-orange-500 to-red-500',
      lightGradient: 'from-orange-50 to-red-100',
      icon: Zap,
      description: 'Emotional sensitivity',
      color: '#F59E0B'
    },
    {
      name: 'Openness',
      key: 'openness_score',
      score: scores.openness_score || 0,
      gradient: 'from-indigo-500 to-purple-500',
      lightGradient: 'from-indigo-50 to-purple-100',
      icon: TrendingUp,
      description: 'Creativity and curiosity',
      color: '#6366F1'
    }
  ];

  const sizeClasses = {
    sm: {
      container: 'p-3',
      title: 'text-sm font-medium',
      bar: 'h-2',
      icon: 'w-3 h-3',
      score: 'text-xs'
    },
    md: {
      container: 'p-4',
      title: 'text-base font-medium',
      bar: 'h-3',
      icon: 'w-4 h-4',
      score: 'text-sm'
    },
    lg: {
      container: 'p-6',
      title: 'text-lg font-semibold',
      bar: 'h-4',
      icon: 'w-5 h-5',
      score: 'text-base'
    }
  };

  const classes = sizeClasses[size];

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Very High';
    if (score >= 60) return 'High';
    if (score >= 40) return 'Moderate';
    if (score >= 20) return 'Low';
    return 'Very Low';
  };

  const hasAnyScores = traits.some(trait => trait.score > 0);

  if (!hasAnyScores) {
    return (
      <div className={`bg-gradient-to-br from-purple-50 to-pink-50 rounded-2xl ${classes.container} text-center border border-purple-100`}>
        <div className="relative">
          <div className="absolute inset-0 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full blur-xl opacity-50"></div>
          <Brain className="w-12 h-12 text-purple-400 mx-auto mb-3 relative z-10" />
        </div>
        <p className="text-gray-600 text-sm font-medium">
          Complete your personality assessment to unlock insights
        </p>
        <p className="text-gray-500 text-xs mt-1">
          Discover your unique personality profile
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-2xl border border-purple-100 shadow-lg hover:shadow-xl transition-all duration-300 ${classes.container}`}>
      {showLabels && (
        <div className="flex items-center mb-6">
          <div className="relative">
            <div className="absolute inset-0 bg-gradient-to-r from-purple-500 to-pink-500 rounded-full blur-sm opacity-20"></div>
            <Brain className={`${classes.icon} text-purple-600 mr-3 relative z-10`} />
          </div>
          <h3 className={`${classes.title} text-gray-800 font-bold`}>
            Personality Profile
          </h3>
        </div>
      )}

      <div className="space-y-4">
        {traits.map((trait, index) => {
          const IconComponent = trait.icon;
          return (
            <div
              key={trait.key}
              className="space-y-2 p-3 rounded-xl hover:bg-gray-50 transition-all duration-200"
              style={{ animationDelay: `${index * 100}ms` }}
            >
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <div className="relative">
                    <div
                      className="absolute inset-0 rounded-full blur-sm opacity-20"
                      style={{ backgroundColor: trait.color }}
                    ></div>
                    <IconComponent
                      className={`${classes.icon} mr-3 relative z-10`}
                      style={{ color: trait.color }}
                    />
                  </div>
                  <span className={`${classes.score} font-semibold text-gray-800`}>
                    {trait.name}
                  </span>
                </div>
                <div className="flex items-center space-x-3">
                  <span className={`${classes.score} font-bold text-gray-900`}>
                    {trait.score}
                  </span>
                  <span className={`${classes.score} text-gray-500 font-medium`}>
                    {getScoreLabel(trait.score)}
                  </span>
                </div>
              </div>

              <div className={`w-full bg-gradient-to-r ${trait.lightGradient} rounded-full ${classes.bar} overflow-hidden`}>
                <div
                  className={`bg-gradient-to-r ${trait.gradient} ${classes.bar} rounded-full transition-all duration-1000 ease-out shadow-sm`}
                  style={{
                    width: `${Math.min(trait.score, 100)}%`,
                    animationDelay: `${index * 200}ms`
                  }}
                />
              </div>

              {size === 'lg' && (
                <p className="text-xs text-gray-600 mt-2 ml-8 italic">
                  {trait.description}
                </p>
              )}
            </div>
          );
        })}
      </div>

      {showInsights && insights && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className={`${classes.title} text-gray-800 mb-2`}>
            Key Insights
          </h4>
          {insights.overallProfile && (
            <p className="text-sm text-gray-600 mb-2">
              {insights.overallProfile}
            </p>
          )}
          {insights.strengths && insights.strengths.length > 0 && (
            <div className="mb-2">
              <span className="text-xs font-medium text-green-700">Strengths: </span>
              <span className="text-xs text-gray-600">
                {insights.strengths.slice(0, 2).join(', ')}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PersonalityVisualization;
