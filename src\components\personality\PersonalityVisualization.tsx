import React from 'react';
import { Brain, TrendingUp, Users, Target, Heart, Zap } from 'lucide-react';

interface PersonalityScores {
  extraversion_score?: number;
  agreeableness_score?: number;
  conscientiousness_score?: number;
  neuroticism_score?: number;
  openness_score?: number;
}

interface PersonalityVisualizationProps {
  scores: PersonalityScores;
  insights?: any;
  size?: 'sm' | 'md' | 'lg';
  showLabels?: boolean;
  showInsights?: boolean;
}

const PersonalityVisualization: React.FC<PersonalityVisualizationProps> = ({
  scores,
  insights,
  size = 'md',
  showLabels = true,
  showInsights = false
}) => {
  const traits = [
    {
      name: 'Extraversion',
      key: 'extraversion_score',
      score: scores.extraversion_score || 0,
      color: 'bg-blue-500',
      lightColor: 'bg-blue-100',
      icon: Users,
      description: 'Social energy and assertiveness'
    },
    {
      name: 'Agreeableness',
      key: 'agreeableness_score',
      score: scores.agreeableness_score || 0,
      color: 'bg-green-500',
      lightColor: 'bg-green-100',
      icon: Heart,
      description: 'Cooperation and trust'
    },
    {
      name: 'Conscientiousness',
      key: 'conscientiousness_score',
      score: scores.conscientiousness_score || 0,
      color: 'bg-purple-500',
      lightColor: 'bg-purple-100',
      icon: Target,
      description: 'Organization and discipline'
    },
    {
      name: 'Neuroticism',
      key: 'neuroticism_score',
      score: scores.neuroticism_score || 0,
      color: 'bg-red-500',
      lightColor: 'bg-red-100',
      icon: Zap,
      description: 'Emotional sensitivity'
    },
    {
      name: 'Openness',
      key: 'openness_score',
      score: scores.openness_score || 0,
      color: 'bg-yellow-500',
      lightColor: 'bg-yellow-100',
      icon: TrendingUp,
      description: 'Creativity and curiosity'
    }
  ];

  const sizeClasses = {
    sm: {
      container: 'p-3',
      title: 'text-sm font-medium',
      bar: 'h-2',
      icon: 'w-3 h-3',
      score: 'text-xs'
    },
    md: {
      container: 'p-4',
      title: 'text-base font-medium',
      bar: 'h-3',
      icon: 'w-4 h-4',
      score: 'text-sm'
    },
    lg: {
      container: 'p-6',
      title: 'text-lg font-semibold',
      bar: 'h-4',
      icon: 'w-5 h-5',
      score: 'text-base'
    }
  };

  const classes = sizeClasses[size];

  const getScoreLabel = (score: number) => {
    if (score >= 80) return 'Very High';
    if (score >= 60) return 'High';
    if (score >= 40) return 'Moderate';
    if (score >= 20) return 'Low';
    return 'Very Low';
  };

  const hasAnyScores = traits.some(trait => trait.score > 0);

  if (!hasAnyScores) {
    return (
      <div className={`bg-gray-50 rounded-lg ${classes.container} text-center`}>
        <Brain className="w-8 h-8 text-gray-400 mx-auto mb-2" />
        <p className="text-gray-500 text-sm">
          No personality assessment completed yet
        </p>
      </div>
    );
  }

  return (
    <div className={`bg-white rounded-lg border ${classes.container}`}>
      {showLabels && (
        <div className="flex items-center mb-4">
          <Brain className={`${classes.icon} text-purple-600 mr-2`} />
          <h3 className={`${classes.title} text-gray-800`}>
            Personality Profile
          </h3>
        </div>
      )}

      <div className="space-y-3">
        {traits.map((trait) => {
          const IconComponent = trait.icon;
          return (
            <div key={trait.key} className="space-y-1">
              <div className="flex items-center justify-between">
                <div className="flex items-center">
                  <IconComponent className={`${classes.icon} text-gray-600 mr-2`} />
                  <span className={`${classes.score} font-medium text-gray-700`}>
                    {trait.name}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={`${classes.score} font-bold text-gray-800`}>
                    {trait.score}
                  </span>
                  <span className={`${classes.score} text-gray-500`}>
                    ({getScoreLabel(trait.score)})
                  </span>
                </div>
              </div>
              
              <div className={`w-full ${trait.lightColor} rounded-full ${classes.bar}`}>
                <div
                  className={`${trait.color} ${classes.bar} rounded-full transition-all duration-500 ease-out`}
                  style={{ width: `${Math.min(trait.score, 100)}%` }}
                />
              </div>
              
              {size === 'lg' && (
                <p className="text-xs text-gray-500 mt-1">
                  {trait.description}
                </p>
              )}
            </div>
          );
        })}
      </div>

      {showInsights && insights && (
        <div className="mt-4 pt-4 border-t border-gray-200">
          <h4 className={`${classes.title} text-gray-800 mb-2`}>
            Key Insights
          </h4>
          {insights.overallProfile && (
            <p className="text-sm text-gray-600 mb-2">
              {insights.overallProfile}
            </p>
          )}
          {insights.strengths && insights.strengths.length > 0 && (
            <div className="mb-2">
              <span className="text-xs font-medium text-green-700">Strengths: </span>
              <span className="text-xs text-gray-600">
                {insights.strengths.slice(0, 2).join(', ')}
              </span>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PersonalityVisualization;
