import React from 'react';

interface ModernToggleProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label?: string;
  description?: string;
  disabled?: boolean;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'default' | 'gradient';
  className?: string;
}

const ModernToggle: React.FC<ModernToggleProps> = ({
  checked,
  onChange,
  label,
  description,
  disabled = false,
  size = 'md',
  variant = 'default',
  className = ''
}) => {
  const sizeClasses = {
    sm: {
      track: 'w-8 h-4',
      thumb: 'w-3 h-3',
      translate: 'translate-x-4'
    },
    md: {
      track: 'w-11 h-6',
      thumb: 'w-5 h-5',
      translate: 'translate-x-5'
    },
    lg: {
      track: 'w-14 h-7',
      thumb: 'w-6 h-6',
      translate: 'translate-x-7'
    }
  };

  const variantClasses = {
    default: {
      trackOn: 'bg-purple-600',
      trackOff: 'bg-gray-200',
      thumb: 'bg-white'
    },
    gradient: {
      trackOn: 'bg-gradient-to-r from-purple-600 to-pink-600',
      trackOff: 'bg-gray-200',
      thumb: 'bg-white'
    }
  };

  const sizes = sizeClasses[size];
  const variants = variantClasses[variant];

  return (
    <div className={`flex items-start space-x-3 ${className}`}>
      <button
        type="button"
        role="switch"
        aria-checked={checked}
        onClick={() => !disabled && onChange(!checked)}
        disabled={disabled}
        className={`
          relative inline-flex flex-shrink-0 border-2 border-transparent rounded-full cursor-pointer
          transition-all duration-200 ease-in-out focus:outline-none focus:ring-2 focus:ring-purple-500 focus:ring-offset-2
          ${sizes.track}
          ${checked ? variants.trackOn : variants.trackOff}
          ${disabled ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-lg'}
        `}
      >
        <span
          className={`
            ${sizes.thumb}
            pointer-events-none inline-block rounded-full shadow-lg transform ring-0 transition-all duration-200 ease-in-out
            ${variants.thumb}
            ${checked ? sizes.translate : 'translate-x-0'}
          `}
        >
          {/* Inner glow effect */}
          <span
            className={`
              absolute inset-0 rounded-full transition-all duration-200
              ${checked ? 'bg-gradient-to-r from-purple-400/20 to-pink-400/20' : 'bg-gray-100/50'}
            `}
          />
        </span>
      </button>

      {(label || description) && (
        <div className="flex-1 min-w-0">
          {label && (
            <label className="text-sm font-semibold text-gray-900 cursor-pointer">
              {label}
            </label>
          )}
          {description && (
            <p className="text-sm text-gray-500 mt-1">
              {description}
            </p>
          )}
        </div>
      )}
    </div>
  );
};

export default ModernToggle;
