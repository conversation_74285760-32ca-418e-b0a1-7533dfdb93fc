import React from 'react';

interface PersonalityScores {
  extraversion_score?: number;
  agreeableness_score?: number;
  conscientiousness_score?: number;
  neuroticism_score?: number;
  openness_score?: number;
}

interface PersonalityRadarChartProps {
  scores: PersonalityScores;
  size?: number;
  showLabels?: boolean;
}

const PersonalityRadarChart: React.FC<PersonalityRadarChartProps> = ({
  scores,
  size = 200,
  showLabels = true
}) => {
  const traits = [
    { name: 'Extraversion', key: 'extraversion_score', shortName: 'E' },
    { name: 'Agreeableness', key: 'agreeableness_score', shortName: 'A' },
    { name: 'Conscientiousness', key: 'conscientiousness_score', shortName: 'C' },
    { name: 'Neuroticism', key: 'neuroticism_score', shortName: 'N' },
    { name: 'Openness', key: 'openness_score', shortName: 'O' }
  ];

  const center = size / 2;
  const radius = (size / 2) - 40;
  const maxScore = 100;

  // Calculate points for the pentagon
  const getPoint = (index: number, score: number) => {
    const angle = (index * 2 * Math.PI) / 5 - Math.PI / 2; // Start from top
    const distance = (score / maxScore) * radius;
    const x = center + distance * Math.cos(angle);
    const y = center + distance * Math.sin(angle);
    return { x, y };
  };

  // Calculate label positions
  const getLabelPoint = (index: number) => {
    const angle = (index * 2 * Math.PI) / 5 - Math.PI / 2;
    const distance = radius + 25;
    const x = center + distance * Math.cos(angle);
    const y = center + distance * Math.sin(angle);
    return { x, y };
  };

  // Create pentagon grid lines
  const gridLevels = [20, 40, 60, 80, 100];
  const gridPaths = gridLevels.map(level => {
    const points = traits.map((_, index) => getPoint(index, level));
    const pathData = points.map((point, index) => 
      `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
    ).join(' ') + ' Z';
    return pathData;
  });

  // Create the data polygon
  const dataPoints = traits.map((trait, index) => {
    const score = scores[trait.key as keyof PersonalityScores] || 0;
    return getPoint(index, score);
  });

  const dataPath = dataPoints.map((point, index) => 
    `${index === 0 ? 'M' : 'L'} ${point.x} ${point.y}`
  ).join(' ') + ' Z';

  // Create axis lines
  const axisLines = traits.map((_, index) => {
    const endPoint = getPoint(index, 100);
    return `M ${center} ${center} L ${endPoint.x} ${endPoint.y}`;
  });

  const hasAnyScores = traits.some(trait => 
    (scores[trait.key as keyof PersonalityScores] || 0) > 0
  );

  if (!hasAnyScores) {
    return (
      <div 
        className="flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300"
        style={{ width: size, height: size }}
      >
        <div className="text-center">
          <div className="text-gray-400 text-sm">No Data</div>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col items-center">
      <svg width={size} height={size} className="overflow-visible">
        {/* Grid lines */}
        {gridPaths.map((path, index) => (
          <path
            key={index}
            d={path}
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="1"
            opacity={0.5}
          />
        ))}

        {/* Axis lines */}
        {axisLines.map((line, index) => (
          <path
            key={index}
            d={line}
            stroke="#d1d5db"
            strokeWidth="1"
            opacity={0.7}
          />
        ))}

        {/* Data area */}
        <path
          d={dataPath}
          fill="rgba(147, 51, 234, 0.2)"
          stroke="#9333ea"
          strokeWidth="2"
        />

        {/* Data points */}
        {dataPoints.map((point, index) => (
          <circle
            key={index}
            cx={point.x}
            cy={point.y}
            r="4"
            fill="#9333ea"
            stroke="white"
            strokeWidth="2"
          />
        ))}

        {/* Labels */}
        {showLabels && traits.map((trait, index) => {
          const labelPoint = getLabelPoint(index);
          const score = scores[trait.key as keyof PersonalityScores] || 0;
          
          return (
            <g key={index}>
              <text
                x={labelPoint.x}
                y={labelPoint.y - 5}
                textAnchor="middle"
                className="text-xs font-medium fill-gray-700"
              >
                {trait.shortName}
              </text>
              <text
                x={labelPoint.x}
                y={labelPoint.y + 8}
                textAnchor="middle"
                className="text-xs fill-gray-500"
              >
                {score}
              </text>
            </g>
          );
        })}

        {/* Center point */}
        <circle
          cx={center}
          cy={center}
          r="2"
          fill="#6b7280"
        />
      </svg>

      {showLabels && (
        <div className="mt-4 grid grid-cols-5 gap-2 text-center">
          {traits.map((trait, index) => (
            <div key={index} className="text-xs">
              <div className="font-medium text-gray-700">{trait.shortName}</div>
              <div className="text-gray-500">{trait.name}</div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
};

export default PersonalityRadarChart;
