import React, { useState, useEffect } from 'react';
import { MessageCircle, Settings, Check, X } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { setContactWillingness, getContactWillingness } from '../../lib/userTagging';

interface ContactWillingnessToggleProps {
  className?: string;
  showLabel?: boolean;
}

const ContactWillingnessToggle: React.FC<ContactWillingnessToggleProps> = ({
  className = '',
  showLabel = true
}) => {
  const { user } = useAuth();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [isOpen, setIsOpen] = useState(false);
  const [statusMessage, setStatusMessage] = useState('');
  const [loading, setLoading] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Load current status on mount
  useEffect(() => {
    if (user?.id) {
      loadContactWillingness();
    }
  }, [user?.id]);

  const loadContactWillingness = async () => {
    if (!user?.id) return;

    try {
      const currentStatus = await getContactWillingness(user.id);
      if (currentStatus) {
        setIsOpen(currentStatus.is_open_to_contact);
        setStatusMessage(currentStatus.status_message || '');
      } else {
        // Default state
        setIsOpen(false);
        setStatusMessage('');
      }
    } catch (err) {
      console.error('Failed to load contact willingness:', err);
      // Set default state if tables don't exist yet
      setIsOpen(false);
      setStatusMessage('');
    }
  };

  const handleToggle = async () => {
    if (!user?.id || loading) return;

    const newIsOpen = !isOpen;
    setLoading(true);
    
    const loadingToastId = showLoading(
      newIsOpen ? 'Setting as open to contact...' : 'Setting as not available...'
    );

    try {
      const result = await setContactWillingness(user.id, newIsOpen, statusMessage);
      
      if (result.success) {
        setIsOpen(newIsOpen);
        showSuccess(
          newIsOpen 
            ? 'You\'re now open to contact! Users who tagged you will be notified.' 
            : 'Contact availability turned off'
        );
        
        // Reload to get updated status
        await loadContactWillingness();
      } else {
        showError(result.error || 'Failed to update contact willingness');
      }
    } catch (err) {
      showError('Failed to update contact willingness');
    } finally {
      setLoading(false);
      dismiss(loadingToastId);
    }
  };

  const handleSaveMessage = async () => {
    if (!user?.id || loading) return;

    setLoading(true);
    const loadingToastId = showLoading('Updating status message...');

    try {
      const result = await setContactWillingness(user.id, isOpen, statusMessage);
      
      if (result.success) {
        showSuccess('Status message updated');
        setShowSettings(false);
        await loadContactWillingness();
      } else {
        showError(result.error || 'Failed to update status message');
      }
    } catch (err) {
      showError('Failed to update status message');
    } finally {
      setLoading(false);
      dismiss(loadingToastId);
    }
  };

  const getStatusDisplay = () => {
    if (isOpen) {
      return {
        color: 'text-green-600',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-200',
        icon: <MessageCircle className="w-4 h-4" />,
        text: 'Open to Contact',
        description: 'Others can see you\'re available to chat'
      };
    } else {
      return {
        color: 'text-gray-600',
        bgColor: 'bg-gray-50',
        borderColor: 'border-gray-200',
        icon: <X className="w-4 h-4" />,
        text: 'Not Available',
        description: 'You\'re not currently open to new contacts'
      };
    }
  };

  const statusDisplay = getStatusDisplay();

  return (
    <div className={`${className}`}>
      <div className={`
        p-4 rounded-lg border-2 transition-all duration-200
        ${statusDisplay.bgColor} ${statusDisplay.borderColor}
      `}>
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className={`${statusDisplay.color}`}>
              {statusDisplay.icon}
            </div>
            <div>
              {showLabel && (
                <div className={`font-medium ${statusDisplay.color}`}>
                  {statusDisplay.text}
                </div>
              )}
              <div className="text-sm text-gray-500">
                {statusDisplay.description}
              </div>
              {statusMessage && (
                <div className="text-sm text-gray-600 mt-1 italic">
                  "{statusMessage}"
                </div>
              )}
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <button
              onClick={() => setShowSettings(!showSettings)}
              className="p-2 text-gray-400 hover:text-gray-600 transition-colors"
              title="Settings"
            >
              <Settings className="w-4 h-4" />
            </button>
            
            <button
              onClick={handleToggle}
              disabled={loading}
              className={`
                relative inline-flex h-6 w-11 items-center rounded-full transition-colors
                ${isOpen ? 'bg-green-500' : 'bg-gray-300'}
                ${loading ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}
              `}
            >
              <span
                className={`
                  inline-block h-4 w-4 transform rounded-full bg-white transition-transform
                  ${isOpen ? 'translate-x-6' : 'translate-x-1'}
                `}
              />
            </button>
          </div>
        </div>

        {showSettings && (
          <div className="mt-4 pt-4 border-t border-gray-200">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status Message (Optional)
            </label>
            <div className="flex gap-2">
              <input
                type="text"
                value={statusMessage}
                onChange={(e) => setStatusMessage(e.target.value)}
                placeholder="e.g., Looking for meaningful conversations..."
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-purple-500 focus:border-transparent"
                maxLength={100}
              />
              <button
                onClick={handleSaveMessage}
                disabled={loading}
                className="px-3 py-2 bg-purple-600 text-white rounded-md text-sm hover:bg-purple-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
              >
                <Check className="w-4 h-4" />
              </button>
            </div>
            <div className="text-xs text-gray-500 mt-1">
              {statusMessage.length}/100 characters
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default ContactWillingnessToggle;
