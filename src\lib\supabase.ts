import { createClient } from '@supabase/supabase-js';
import type { Database } from './database.types';
import { <PERSON><PERSON><PERSON>Logger } from './errorHandler';
const supabaseUrl = import.meta.env?.VITE_SUPABASE_URL || process.env.VITE_SUPABASE_URL;
const supabaseAnonKey = import.meta.env?.VITE_SUPABASE_ANON_KEY || process.env.VITE_SUPABASE_ANON_KEY;


if (!supabaseUrl || !supabaseAnonKey) {
  throw new Error('Missing Supabase environment variables. Please check your .env file.');
}

// ────────────────────────────────────────────────────────────
// Clean & validate the URL (allow http for local development)
// ────────────────────────────────────────────────────────────
const cleanSupabaseUrl = supabaseUrl.startsWith('http://') ? supabaseUrl : supabaseUrl.replace(/^(https:\/\/+)(.*)/i, 'https://$2');

try {
  const url = new URL(cleanSupabaseUrl);
  // Allow HTTP for local development (127.0.0.1 or localhost)
  const isLocalDev = url.hostname === '127.0.0.1' || url.hostname === 'localhost';
  if (!isLocalDev && url.protocol !== 'https:') {
    throw new Error('Supabase URL must use HTTPS protocol in production');
  }
} catch (error) {
  if (error instanceof Error && error.message.includes('HTTPS protocol')) {
    throw error;
  }
  throw new Error('Invalid Supabase URL format. Check VITE_SUPABASE_URL.');
}

// ────────────────────────────────────────────────────────────
// Secure local‑storage wrapper (never throws on “not found”)
// ────────────────────────────────────────────────────────────
const secureStorage = {
  getItem: async (key: string) => {
    try {
      return localStorage.getItem(key);
    } catch (error) {
      ErrorLogger.logError(error, 'auth', JSON.stringify({ action: 'getItem', key }));
      return null;
    }
  },
  setItem: async (key: string, value: string) => {
    try {
      localStorage.setItem(key, value);
    } catch (error) {
      ErrorLogger.logError(error, 'auth', JSON.stringify({ action: 'setItem', key }));
      throw error;
    }
  },
  removeItem: async (key: string) => {
    try {
      localStorage.removeItem(key);
    } catch (error) {
      ErrorLogger.logError(error, 'auth', JSON.stringify({ action: 'removeItem', key }));
      throw error;
    }
  }
};

// ────────────────────────────────────────────────────────────
// Supabase client (v2) – options align with current typings
// ────────────────────────────────────────────────────────────
export const supabase = createClient<Database>(cleanSupabaseUrl, supabaseAnonKey, {
  auth: {
    autoRefreshToken: true,
    persistSession: true,
    detectSessionInUrl: true,
    debug: (import.meta.env?.MODE || process.env.NODE_ENV) === 'development',
    storage: secureStorage
  },
  realtime: {
    params: { eventsPerSecond: 10 },
    heartbeatIntervalMs: 8000
  },
  global: {
    headers: {
      'x-application-name': 'soultwin-sync',
      'x-client-info': 'supabase-js-web'
    }
  }
});

// ────────────────────────────────────────────────────────────
// Auth‑state logging (optional)
// ────────────────────────────────────────────────────────────
supabase.auth.onAuthStateChange((event, session) => {
  const metadata = { event, timestamp: new Date().toISOString(), userId: session?.user?.id };
  console.log(`Auth state changed: ${event}`, metadata);
});

// ────────────────────────────────────────────────────────────
// Connection‑verification helper with retries
// ────────────────────────────────────────────────────────────
export const verifyConnection = async (retries = 3) => {
  const attempt = async (n: number): Promise<{ status: string; error: any }> => {
    try {
      const { data: { session }, error: authError } = await supabase.auth.getSession();
      if (authError) throw authError;

      if (!session) return { status: 'unauthenticated', error: null };

      const { error: dbError } = await supabase.from('profiles')
        .select('id', { head: true, count: 'exact' });
      if (dbError) throw dbError;

      return { status: 'connected', error: null };
    } catch (error) {
      if (n < retries) {
        await new Promise(res => setTimeout(res, 1000 * n));
        return attempt(n + 1);
      }
      ErrorLogger.logError(error, 'system', JSON.stringify({ attemptNumber: n }));
      return {
        status: 'error',
        error: {
          message: 'Connection verification failed',
          details: error instanceof Error ? error.message : 'Unknown',
          code: error instanceof Error ? error.name : 'CONN_ERROR',
          attempt: n
        }
      };
    }
  };
  return attempt(1);
};

// ────────────────────────────────────────────────────────────
// Basic HTTP health‑check
// ────────────────────────────────────────────────────────────
export const checkConnectionHealth = async () => {
  try {
    const start = performance.now();
    const response = await fetch(`${cleanSupabaseUrl}/rest/v1/`, {
      headers: { apikey: supabaseAnonKey, 'Content-Type': 'application/json' }
    });
    const latency = performance.now() - start;

    return {
      status: response.ok ? 'healthy' : 'unhealthy',
      statusCode: response.status,
      latency,
      headers: Object.fromEntries(response.headers),
      timestamp: new Date().toISOString()
    };
  } catch (error) {
    ErrorLogger.logError(error, 'system', JSON.stringify({ service: 'health-check' }));
    return {
      status: 'error',
      error: error instanceof Error ? error.message : 'Connection check failed',
      timestamp: new Date().toISOString()
    };
  }
};
