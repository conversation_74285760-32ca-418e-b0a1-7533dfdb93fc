import React, { useState } from 'react';
import { Link, useLocation, useNavigate } from 'react-router-dom';
import { Menu, X, User, MessageCircle, Settings, Heart, LogOut, Search, Sparkles } from 'lucide-react';
import { useProfile } from '../../context/ProfileContext';
import { useAuth } from '../auth/AuthProvider';
import { signOut } from '../../lib/auth';
import { useToast } from '../../hooks/useToast';
import NotificationPanel from '../notifications/NotificationPanel';

export const Header: React.FC = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const { profile } = useProfile();
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();
  const { showSuccess, showError } = useToast();

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const isActive = (path: string) => {
    return location.pathname === path;
  };

  const handleLogout = async () => {
    try {
      const { success, error } = await signOut();
      if (success) {
        showSuccess('Successfully logged out');
        navigate('/login');
      } else {
        throw error;
      }
    } catch (error) {
      showError('Failed to log out. Please try again.');
    }
  };

  return (
    <header className="gradient-primary text-white shadow-xl relative overflow-hidden">
      {/* Background decoration */}
      <div className="absolute inset-0 bg-gradient-to-r from-purple-600/20 to-pink-500/20"></div>
      <div className="absolute top-0 left-0 w-full h-full">
        <div className="absolute top-4 left-4 w-32 h-32 bg-white/5 rounded-full blur-xl"></div>
        <div className="absolute top-8 right-8 w-24 h-24 bg-white/5 rounded-full blur-lg"></div>
      </div>

      <div className="container mx-auto px-6 py-4 relative z-10">
        <div className="flex justify-between items-center">
          <Link to="/" className="flex items-center space-x-3 group">
            <div className="relative">
              <Heart className="h-10 w-10 text-white group-hover:scale-110 transition-transform duration-300" />
              <Sparkles className="h-4 w-4 text-pink-200 absolute -top-1 -right-1 animate-pulse" />
            </div>
            <div className="flex flex-col">
              <span className="text-2xl font-bold text-white tracking-tight">
                SOULSYNK
              </span>
              <span className="text-xs text-pink-100 font-medium tracking-wider">
                Find Your Digital Twin
              </span>
            </div>
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex items-center space-x-2 glass rounded-2xl px-6 py-3">
            <Link
              to="/dashboard"
              className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                isActive('/dashboard')
                  ? 'bg-white/20 text-white shadow-lg'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
            >
              Dashboard
            </Link>
            <Link
              to="/discover"
              className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                isActive('/discover')
                  ? 'bg-white/20 text-white shadow-lg'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
            >
              Discover
            </Link>
            <Link
              to="/conversations"
              className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                isActive('/conversations')
                  ? 'bg-white/20 text-white shadow-lg'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
            >
              Conversations
            </Link>
            <Link
              to="/settings"
              className={`px-4 py-2 rounded-xl font-medium transition-all duration-300 ${
                isActive('/settings')
                  ? 'bg-white/20 text-white shadow-lg'
                  : 'text-white/80 hover:text-white hover:bg-white/10'
              }`}
            >
              Settings
            </Link>
            {user ? (
              <div className="flex items-center space-x-4">
                {/* Notification Panel */}
                <NotificationPanel />

                <div className="flex items-center space-x-2 bg-white/20 rounded-full px-4 py-2">
                  <div className="w-8 h-8 rounded-full bg-purple-300 flex items-center justify-center text-purple-800 font-bold">
                    {profile?.name?.charAt(0) || user.email?.charAt(0) || 'U'}
                  </div>
                  <span>{profile?.name || 'User'}</span>
                </div>
                <button
                  onClick={handleLogout}
                  className="flex items-center space-x-1 hover:text-pink-200 transition duration-200"
                >
                  <LogOut className="h-5 w-5" />
                  <span>Logout</span>
                </button>
              </div>
            ) : (
              <Link 
                to="/login" 
                className="bg-white text-purple-600 hover:bg-pink-100 transition duration-200 px-4 py-2 rounded-full font-semibold"
              >
                Sign In
              </Link>
            )}
          </nav>

          {/* Mobile menu button */}
          <button 
            className="md:hidden text-white focus:outline-none" 
            onClick={toggleMenu}
            aria-label="Toggle menu"
          >
            {isMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <nav className="md:hidden mt-4 bg-white/10 backdrop-blur-sm rounded-lg p-4 animate-fadeIn">
            <ul className="space-y-3">
              <li>
                <Link 
                  to="/dashboard" 
                  className={`block px-4 py-2 rounded hover:bg-white/20 transition ${
                    isActive('/dashboard') ? 'bg-white/20 font-semibold' : ''
                  }`}
                  onClick={closeMenu}
                >
                  <div className="flex items-center space-x-3">
                    <User className="h-5 w-5" />
                    <span>Dashboard</span>
                  </div>
                </Link>
              </li>
              <li>
                <Link
                  to="/discover"
                  className={`block px-4 py-2 rounded hover:bg-white/20 transition ${
                    isActive('/discover') ? 'bg-white/20 font-semibold' : ''
                  }`}
                  onClick={closeMenu}
                >
                  <div className="flex items-center space-x-3">
                    <Search className="h-5 w-5" />
                    <span>Discover</span>
                  </div>
                </Link>
              </li>
              <li>
                <Link
                  to="/conversations"
                  className={`block px-4 py-2 rounded hover:bg-white/20 transition ${
                    isActive('/conversations') ? 'bg-white/20 font-semibold' : ''
                  }`}
                  onClick={closeMenu}
                >
                  <div className="flex items-center space-x-3">
                    <MessageCircle className="h-5 w-5" />
                    <span>Conversations</span>
                  </div>
                </Link>
              </li>
              <li>
                <Link 
                  to="/settings" 
                  className={`block px-4 py-2 rounded hover:bg-white/20 transition ${
                    isActive('/settings') ? 'bg-white/20 font-semibold' : ''
                  }`}
                  onClick={closeMenu}
                >
                  <div className="flex items-center space-x-3">
                    <Settings className="h-5 w-5" />
                    <span>Settings</span>
                  </div>
                </Link>
              </li>
              {user ? (
                <>
                  <li className="border-t border-white/20 pt-2">
                    <div className="px-4 py-2 text-sm opacity-75">
                      Signed in as {profile?.name || user.email}
                    </div>
                  </li>
                  <li>
                    <button
                      onClick={() => {
                        handleLogout();
                        closeMenu();
                      }}
                      className="w-full px-4 py-2 flex items-center space-x-3 text-left rounded hover:bg-white/20 transition"
                    >
                      <LogOut className="h-5 w-5" />
                      <span>Logout</span>
                    </button>
                  </li>
                </>
              ) : (
                <li className="pt-2 border-t border-white/20">
                  <Link 
                    to="/login" 
                    className="block bg-white text-purple-600 hover:bg-pink-100 transition px-4 py-2 rounded-lg font-semibold text-center"
                    onClick={closeMenu}
                  >
                    Sign In
                  </Link>
                </li>
              )}
            </ul>
          </nav>
        )}
      </div>
    </header>
  );
};