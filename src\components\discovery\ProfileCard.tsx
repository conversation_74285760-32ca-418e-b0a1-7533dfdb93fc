import React, { useState } from 'react';
import { UserCircle, MapPin, Heart, MessageCircle, Star, Sparkles, Camera } from 'lucide-react';
import { Profile } from '../../types';
import UserBadges from '../tagging/UserBadges';
import { PersonalitySummary } from '../personality';
import styles from './ProfileCard.module.css';

/**
 * @file ProfileCard.tsx
 * @description Component to display a summary of a user's profile in a card format.
 * Used in discovery sections to provide a quick overview of users.
 */

interface ProfileCardProps {
  /** The profile data to display on the card. */
  profile: Profile;
  /** Optional click handler for when the card is clicked. */
  onClick?: () => void;
}

const ProfileCard: React.FC<ProfileCardProps> = ({ profile, onClick }) => {
  // // Function to get initials for placeholder avatar - Currently UserCircle icon is used.
  // const getInitials = (name: string): string => {
  //   if (!name) return '?';
  //   const names = name.split(' ');
  //   const initials = names.map(n => n[0]).join('');
  //   return initials.length > 2 ? initials.substring(0, 2) : initials;
  // };

  /**
   * Truncates a string (bio) to a specified maximum length, appending "..." if truncated.
   * @param bio The biography string to truncate.
   * @param maxLength The maximum length before truncation. Defaults to 100.
   * @returns The truncated bio string or a default message if no bio is available.
   */
  const truncateBio = (bio: string | undefined, maxLength: number = 100): string => {
    if (!bio || bio.trim() === '') return 'No bio available.'; // Handle empty or whitespace-only bios
    if (bio.length <= maxLength) return bio;
    return `${bio.substring(0, maxLength)}...`;
  };

  return (
    <div
      className={styles.profileCard}
      onClick={onClick}
      tabIndex={0}
      role="button"
      aria-label={`View ${profile.name}'s profile`}
    >
      {/* Avatar Section - Fixed height for consistency */}
      <div className={styles.avatarSection}>
        {profile.avatar_url ? (
          <img
            src={profile.avatar_url}
            alt={`${profile.name}'s avatar`}
            className={styles.avatar}
          />
        ) : (
          <div className={styles.avatarPlaceholder}>
            <UserCircle className="w-16 h-16 text-gray-500" />
            {/* Fallback to initials if UserCircle is too generic or not preferred
            <span className="text-3xl font-semibold text-gray-600">{getInitials(profile.name)}</span>
            */}
          </div>
        )}
      </div>

      {/* Content Section - Using CSS Module for consistent layout */}
      <div className={styles.contentSection}>
        {/* Header Section - Name and Age/Gender */}
        <div className={styles.headerSection}>
          <h2 className={styles.profileName} title={profile.name}>
            {profile.name || 'Unnamed User'}
          </h2>
          <div className={styles.profileDetails}>
            {(profile.age || profile.gender) && (
              <p className={styles.profileDetailsText}>
                {profile.age && <span>{profile.age}</span>}
                {profile.age && profile.gender && <span>, </span>}
                {profile.gender && <span>{profile.gender}</span>}
              </p>
            )}
          </div>
        </div>

        {/* Bio Section - Fixed height for consistency */}
        <div className={styles.bioSection}>
          <div className={styles.bioContainer}>
            <p className={styles.bioText}>
              {truncateBio(profile.bio)}
            </p>
          </div>
        </div>

        {/* Personality Summary Section */}
        <div className="px-2 py-2">
          <PersonalitySummary
            scores={{
              extraversion_score: profile.extraversion_score,
              agreeableness_score: profile.agreeableness_score,
              conscientiousness_score: profile.conscientiousness_score,
              neuroticism_score: profile.neuroticism_score,
              openness_score: profile.openness_score,
              personality_insights: profile.personality_insights
            }}
            variant="compact"
            showInsights={false}
          />
        </div>

        {/* User Badges Section - Fixed height */}
        <div className={styles.badgesSection}>
          <div className={styles.badgesContainer}>
            <UserBadges
              userId={profile.id}
              size="sm"
              className=""
            />
          </div>
        </div>

        {/* Interests Section - Fixed height and consistent layout */}
        <div className={styles.interestsSection}>
          {profile.interests && profile.interests.length > 0 ? (
            <div>
              <h4 className={styles.interestsHeader}>
                Interests
              </h4>
              <div className={styles.interestsContainer}>
                {profile.interests.slice(0, 3).map((interest, index) => (
                  <span
                    key={index}
                    className={styles.interestTag}
                  >
                    {typeof interest === 'string' ? interest : interest.name}
                  </span>
                ))}
                {profile.interests.length > 3 && (
                  <span className={styles.moreInterestsTag}>
                    +{profile.interests.length - 3} more
                  </span>
                )}
              </div>
            </div>
          ) : (
            <div className={styles.noInterests}>
              No interests listed
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default ProfileCard;
