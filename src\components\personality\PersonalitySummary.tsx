import React from 'react';
import { <PERSON>, <PERSON>rk<PERSON> } from 'lucide-react';

interface PersonalityScores {
  extraversion_score?: number;
  agreeableness_score?: number;
  conscientiousness_score?: number;
  neuroticism_score?: number;
  openness_score?: number;
  personality_insights?: any;
}

interface PersonalitySummaryProps {
  scores: PersonalityScores;
  variant?: 'compact' | 'detailed';
  showInsights?: boolean;
}

const PersonalitySummary: React.FC<PersonalitySummaryProps> = ({
  scores,
  variant = 'compact',
  showInsights = false
}) => {
  const traits = [
    {
      name: 'Extraversion',
      key: 'extraversion_score',
      short: 'E',
      score: scores.extraversion_score || 0,
      color: 'text-blue-600 bg-blue-100'
    },
    {
      name: 'Agreeableness',
      key: 'agreeableness_score',
      short: 'A',
      score: scores.agreeableness_score || 0,
      color: 'text-green-600 bg-green-100'
    },
    {
      name: 'Conscientiousness',
      key: 'conscientiousness_score',
      short: 'C',
      score: scores.conscientiousness_score || 0,
      color: 'text-purple-600 bg-purple-100'
    },
    {
      name: 'Neuroticism',
      key: 'neuroticism_score',
      short: 'N',
      score: scores.neuroticism_score || 0,
      color: 'text-red-600 bg-red-100'
    },
    {
      name: 'Openness',
      key: 'openness_score',
      short: 'O',
      score: scores.openness_score || 0,
      color: 'text-yellow-600 bg-yellow-100'
    }
  ];

  const hasAnyScores = traits.some(trait => trait.score > 0);

  const getPersonalityType = () => {
    if (!hasAnyScores) return null;

    const highTraits = traits.filter(trait => trait.score >= 70);
    const lowTraits = traits.filter(trait => trait.score <= 30);

    if (highTraits.length === 0 && lowTraits.length === 0) {
      return "Balanced personality";
    }

    let description = "";
    if (scores.extraversion_score && scores.extraversion_score >= 70) {
      description += "Outgoing ";
    } else if (scores.extraversion_score && scores.extraversion_score <= 30) {
      description += "Introspective ";
    }

    if (scores.openness_score && scores.openness_score >= 70) {
      description += "Creative ";
    } else if (scores.openness_score && scores.openness_score <= 30) {
      description += "Practical ";
    }

    if (scores.conscientiousness_score && scores.conscientiousness_score >= 70) {
      description += "Organized ";
    }

    if (scores.agreeableness_score && scores.agreeableness_score >= 70) {
      description += "Cooperative ";
    }

    return description.trim() || "Unique personality";
  };

  const getTopTraits = () => {
    return traits
      .filter(trait => trait.score > 0)
      .sort((a, b) => b.score - a.score)
      .slice(0, 3);
  };

  if (!hasAnyScores) {
    return (
      <div className="flex items-center text-gray-500 text-sm">
        <Brain className="w-4 h-4 mr-2" />
        <span>Personality assessment pending</span>
      </div>
    );
  }

  if (variant === 'compact') {
    const topTraits = getTopTraits();
    
    return (
      <div className="space-y-2">
        <div className="flex items-center">
          <Brain className="w-4 h-4 text-purple-600 mr-2" />
          <span className="text-sm font-medium text-gray-700">
            {getPersonalityType()}
          </span>
        </div>
        
        <div className="flex flex-wrap gap-1">
          {topTraits.map((trait) => (
            <div
              key={trait.key}
              className={`px-2 py-1 rounded-full text-xs font-medium ${trait.color}`}
              title={`${trait.name}: ${trait.score}/100`}
            >
              {trait.short}{trait.score}
            </div>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      <div className="flex items-center">
        <Brain className="w-5 h-5 text-purple-600 mr-2" />
        <h3 className="text-base font-semibold text-gray-800">
          Personality Profile
        </h3>
      </div>

      <div className="text-sm text-gray-600 mb-3">
        {getPersonalityType()}
      </div>

      <div className="grid grid-cols-5 gap-2">
        {traits.map((trait) => (
          <div key={trait.key} className="text-center">
            <div
              className={`w-8 h-8 rounded-full flex items-center justify-center text-xs font-bold mx-auto mb-1 ${trait.color}`}
            >
              {trait.short}
            </div>
            <div className="text-xs text-gray-600">{trait.score}</div>
            <div className="text-xs text-gray-500 truncate">{trait.name}</div>
          </div>
        ))}
      </div>

      {showInsights && scores.personality_insights && (
        <div className="mt-3 p-3 bg-purple-50 rounded-lg">
          <div className="flex items-center mb-2">
            <Sparkles className="w-4 h-4 text-purple-600 mr-1" />
            <span className="text-sm font-medium text-purple-800">
              Key Insights
            </span>
          </div>
          
          {scores.personality_insights.overallProfile && (
            <p className="text-sm text-purple-700 mb-2">
              {scores.personality_insights.overallProfile}
            </p>
          )}
          
          {scores.personality_insights.strengths && 
           scores.personality_insights.strengths.length > 0 && (
            <div className="text-xs text-purple-600">
              <span className="font-medium">Strengths: </span>
              {scores.personality_insights.strengths.slice(0, 2).join(', ')}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default PersonalitySummary;
