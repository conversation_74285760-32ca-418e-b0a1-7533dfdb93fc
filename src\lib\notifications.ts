import { supabase } from './supabase';
import { <PERSON><PERSON>r<PERSON>ogger } from './errorHandler';
import { UserNotification } from '../types';

/**
 * Notification Service
 * Handles user notifications for tagging events
 */

/**
 * Get all notifications for a user
 */
export async function getUserNotifications(
  userId: string,
  limit: number = 50
): Promise<UserNotification[]> {
  try {
    const { data, error } = await supabase
      .from('user_notifications')
      .select('*')
      .eq('user_id', userId)
      .order('created_at', { ascending: false })
      .limit(limit);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return [];
    }

    return data || [];
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return [];
  }
}

/**
 * Get unread notification count for a user
 */
export async function getUnreadNotificationCount(userId: string): Promise<number> {
  try {
    const { count, error } = await supabase
      .from('user_notifications')
      .select('*', { count: 'exact', head: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return 0;
    }

    return count || 0;
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return 0;
  }
}

/**
 * Mark a notification as read
 */
export async function markNotificationAsRead(
  notificationId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .update({ read: true })
      .eq('id', notificationId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to mark notification as read' };
  }
}

/**
 * Mark all notifications as read for a user
 */
export async function markAllNotificationsAsRead(
  userId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .update({ read: true })
      .eq('user_id', userId)
      .eq('read', false);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to mark all notifications as read' };
  }
}

/**
 * Delete a notification
 */
export async function deleteNotification(
  notificationId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const { error } = await supabase
      .from('user_notifications')
      .delete()
      .eq('id', notificationId);

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to delete notification' };
  }
}

/**
 * Create a custom notification
 */
export async function createNotification(
  userId: string,
  type: UserNotification['type'],
  title: string,
  message: string,
  data?: any
): Promise<{ success: boolean; error?: string; notification?: UserNotification }> {
  try {
    const { data: notification, error } = await supabase
      .from('user_notifications')
      .insert({
        user_id: userId,
        type,
        title,
        message,
        data
      })
      .select('*')
      .single();

    if (error) {
      ErrorLogger.logError(error, 'database');
      return { success: false, error: error.message };
    }

    return { success: true, notification };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { success: false, error: 'Failed to create notification' };
  }
}

/**
 * Subscribe to real-time notifications for a user
 */
export function subscribeToNotifications(
  userId: string,
  onNotification: (notification: UserNotification) => void
) {
  const channel = supabase
    .channel(`notifications:${userId}`)
    .on(
      'postgres_changes',
      {
        event: 'INSERT',
        schema: 'public',
        table: 'user_notifications',
        filter: `user_id=eq.${userId}`,
      },
      (payload) => {
        const notification = payload.new as UserNotification;
        onNotification(notification);
      }
    )
    .subscribe();

  return channel;
}

/**
 * Get notification display information
 */
export function getNotificationDisplayInfo(notification: UserNotification) {
  const icons = {
    interest_tag: '💫',
    contact_willingness: '🔔',
    mutual_interest: '💖'
  };

  const colors = {
    interest_tag: 'text-purple-600',
    contact_willingness: 'text-blue-600',
    mutual_interest: 'text-pink-600'
  };

  return {
    icon: icons[notification.type] || '📢',
    color: colors[notification.type] || 'text-gray-600',
    timeAgo: getTimeAgo(notification.created_at)
  };
}

/**
 * Helper function to get time ago string
 */
function getTimeAgo(dateString: string): string {
  const date = new Date(dateString);
  const now = new Date();
  const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);

  if (diffInSeconds < 60) {
    return 'Just now';
  } else if (diffInSeconds < 3600) {
    const minutes = Math.floor(diffInSeconds / 60);
    return `${minutes}m ago`;
  } else if (diffInSeconds < 86400) {
    const hours = Math.floor(diffInSeconds / 3600);
    return `${hours}h ago`;
  } else if (diffInSeconds < 604800) {
    const days = Math.floor(diffInSeconds / 86400);
    return `${days}d ago`;
  } else {
    return date.toLocaleDateString();
  }
}
