interface PersonalityScores {
  extraversion_score?: number;
  agreeableness_score?: number;
  conscientiousness_score?: number;
  neuroticism_score?: number;
  openness_score?: number;
}

/**
 * Calculates personality compatibility between two users based on Big Five scores
 * @param userScores - First user's personality scores
 * @param otherScores - Second user's personality scores
 * @returns Compatibility score from 0-100
 */
export function calculatePersonalityCompatibility(
  userScores: PersonalityScores,
  otherScores: PersonalityScores
): number {
  const traits = [
    'extraversion_score',
    'agreeableness_score',
    'conscientiousness_score',
    'neuroticism_score',
    'openness_score'
  ] as const;

  let totalDifference = 0;
  let validTraits = 0;

  traits.forEach(trait => {
    const userScore = userScores[trait] || 0;
    const otherScore = otherScores[trait] || 0;
    
    if (userScore > 0 && otherScore > 0) {
      totalDifference += Math.abs(userScore - otherScore);
      validTraits++;
    }
  });

  if (validTraits === 0) return 0;

  // Convert difference to compatibility score (0-100)
  const averageDifference = totalDifference / validTraits;
  const compatibility = Math.max(0, 100 - averageDifference);
  return Math.round(compatibility);
}

/**
 * Filters profiles based on personality compatibility threshold
 * @param profiles - Array of profiles to filter
 * @param userScores - Current user's personality scores
 * @param minCompatibility - Minimum compatibility score (default: 70)
 * @returns Filtered array of profiles with high compatibility
 */
export function filterByPersonalityCompatibility(
  profiles: any[],
  userScores: PersonalityScores,
  minCompatibility: number = 70
): any[] {
  return profiles.filter(profile => {
    const compatibility = calculatePersonalityCompatibility(userScores, {
      extraversion_score: profile.extraversion_score,
      agreeableness_score: profile.agreeableness_score,
      conscientiousness_score: profile.conscientiousness_score,
      neuroticism_score: profile.neuroticism_score,
      openness_score: profile.openness_score
    });
    
    return compatibility >= minCompatibility;
  });
}

/**
 * Sorts profiles by personality compatibility in descending order
 * @param profiles - Array of profiles to sort
 * @param userScores - Current user's personality scores
 * @returns Sorted array of profiles
 */
export function sortByPersonalityCompatibility(
  profiles: any[],
  userScores: PersonalityScores
): any[] {
  return profiles.sort((a, b) => {
    const compatibilityA = calculatePersonalityCompatibility(userScores, {
      extraversion_score: a.extraversion_score,
      agreeableness_score: a.agreeableness_score,
      conscientiousness_score: a.conscientiousness_score,
      neuroticism_score: a.neuroticism_score,
      openness_score: a.openness_score
    });
    
    const compatibilityB = calculatePersonalityCompatibility(userScores, {
      extraversion_score: b.extraversion_score,
      agreeableness_score: b.agreeableness_score,
      conscientiousness_score: b.conscientiousness_score,
      neuroticism_score: b.neuroticism_score,
      openness_score: b.openness_score
    });
    
    return compatibilityB - compatibilityA; // Descending order
  });
}

/**
 * Gets compatibility level description
 * @param score - Compatibility score (0-100)
 * @returns Object with level and description
 */
export function getCompatibilityLevel(score: number) {
  if (score >= 85) return { level: 'Excellent', description: 'Very high compatibility' };
  if (score >= 70) return { level: 'Good', description: 'Good compatibility' };
  if (score >= 55) return { level: 'Moderate', description: 'Moderate compatibility' };
  if (score >= 40) return { level: 'Fair', description: 'Some differences' };
  return { level: 'Low', description: 'Significant differences' };
}
