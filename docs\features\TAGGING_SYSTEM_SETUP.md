# User Tagging System Setup Guide

## Overview

The user tagging system allows users to express interest in connecting with other users and set their availability for contact. It includes:

1. **Interest Tags**: Users can tag others with different levels of interest
2. **Contact Willingness**: Users can indicate when they're open to being contacted
3. **Notifications**: Real-time notifications when tagged users become available
4. **Visual Badges**: Display interest levels and availability status on profiles

## Database Setup

### Step 1: Create Database Tables

1. Go to your Supabase dashboard
2. Navigate to the SQL Editor
3. Copy and paste the contents of `manual_setup.sql` into the editor
4. Run the SQL script

This will create:
- `user_interest_tags` table
- `contact_willingness_status` table  
- `user_notifications` table
- All necessary RLS policies and indexes
- Trigger function for automatic notifications

### Step 2: Verify Tables

After running the script, verify the tables were created:

```sql
SELECT table_name FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('user_interest_tags', 'contact_willingness_status', 'user_notifications');
```

## Features

### 1. Interest Tagging

Users can tag other profiles with three levels of interest:
- **Interested** 👋 - Basic interest in connecting
- **Very Interested** 💫 - Strong interest in connecting  
- **Potential Match** 💖 - See great potential for connection

### 2. Contact Willingness

Users can set their availability status:
- Toggle on/off their openness to contact
- Add optional status message
- When enabled, users who tagged them get notified

### 3. Notifications

Real-time notification system:
- Get notified when someone tags you
- Get notified when someone you tagged becomes available
- Mark notifications as read/unread
- Delete notifications

### 4. Visual Badges

Profile badges show:
- Contact availability status
- Number of users who have shown interest
- Interest level breakdown

## Usage

### For Users

1. **Browse profiles** on the Discover page
2. **Tag interest** using the "Tag Interest" button on profiles
3. **Set availability** in Settings > Contact Availability
4. **View notifications** using the bell icon in the header
5. **See badges** on profiles showing interest and availability

### For Developers

The system is modular with these key components:

#### Services
- `src/lib/userTagging.ts` - Core tagging functionality
- `src/lib/notifications.ts` - Notification management

#### Components
- `src/components/tagging/InterestTagButton.tsx` - Tag other users
- `src/components/tagging/ContactWillingnessToggle.tsx` - Set availability
- `src/components/tagging/UserBadges.tsx` - Display badges
- `src/components/notifications/NotificationPanel.tsx` - Show notifications

#### Pages
- Updated `src/pages/Discover.tsx` with tagging features
- Updated `src/pages/Settings.tsx` with contact willingness
- Added `src/pages/TaggingDemo.tsx` for demonstration

## Testing

### Demo Page

Visit `/tagging-demo` to see all features in action:
- Contact willingness toggle
- Interest tagging demonstration
- Badge display examples
- Notification panel

### Manual Testing

1. Create two user accounts
2. Have User A tag User B with interest
3. Have User B set contact willingness to "open"
4. User A should receive a notification
5. Check that badges appear on profiles

## Error Handling

The system gracefully handles missing database tables:
- Components show default states if tables don't exist
- No errors are thrown if database operations fail
- Users can still browse profiles normally

## Future Enhancements

Potential improvements:
- Mutual interest detection and special notifications
- Interest tag expiration
- Advanced filtering by interest levels
- Analytics dashboard for popular users
- Integration with messaging system

## Troubleshooting

### Common Issues

1. **Tables not found**: Run the `manual_setup.sql` script
2. **RLS errors**: Check that policies are created correctly
3. **Notifications not working**: Verify the trigger function exists
4. **Components not loading**: Check browser console for errors

### Debug Mode

Enable debug logging by adding to your `.env`:
```
VITE_DEBUG_TAGGING=true
```

This will show detailed logs for tagging operations.
