import React from 'react';
import { Heart, Sparkles } from 'lucide-react';

interface ModernLoadingSpinnerProps {
  size?: 'sm' | 'md' | 'lg';
  message?: string;
  variant?: 'default' | 'hearts' | 'dots' | 'pulse';
}

const ModernLoadingSpinner: React.FC<ModernLoadingSpinnerProps> = ({
  size = 'md',
  message = 'Loading...',
  variant = 'default'
}) => {
  const sizeClasses = {
    sm: 'w-6 h-6',
    md: 'w-8 h-8',
    lg: 'w-12 h-12'
  };

  const containerSizeClasses = {
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  };

  if (variant === 'hearts') {
    return (
      <div className={`flex flex-col items-center justify-center ${containerSizeClasses[size]}`}>
        <div className="relative">
          <Heart className={`${sizeClasses[size]} text-pink-500 animate-pulse`} />
          <Heart className={`${sizeClasses[size]} text-purple-500 absolute top-0 left-0 animate-ping`} />
        </div>
        {message && (
          <p className="mt-3 text-sm text-gray-600 animate-pulse">{message}</p>
        )}
      </div>
    );
  }

  if (variant === 'dots') {
    return (
      <div className={`flex flex-col items-center justify-center ${containerSizeClasses[size]}`}>
        <div className="flex space-x-1">
          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '0ms' }}></div>
          <div className="w-2 h-2 bg-pink-500 rounded-full animate-bounce" style={{ animationDelay: '150ms' }}></div>
          <div className="w-2 h-2 bg-purple-500 rounded-full animate-bounce" style={{ animationDelay: '300ms' }}></div>
        </div>
        {message && (
          <p className="mt-3 text-sm text-gray-600">{message}</p>
        )}
      </div>
    );
  }

  if (variant === 'pulse') {
    return (
      <div className={`flex flex-col items-center justify-center ${containerSizeClasses[size]}`}>
        <div className="relative">
          <div className={`${sizeClasses[size]} bg-gradient-to-r from-purple-500 to-pink-500 rounded-full animate-pulse`}></div>
          <div className={`${sizeClasses[size]} bg-gradient-to-r from-purple-400 to-pink-400 rounded-full absolute top-0 left-0 animate-ping opacity-75`}></div>
        </div>
        {message && (
          <p className="mt-3 text-sm text-gray-600">{message}</p>
        )}
      </div>
    );
  }

  // Default spinner
  return (
    <div className={`flex flex-col items-center justify-center ${containerSizeClasses[size]}`}>
      <div className="relative">
        <div className={`${sizeClasses[size]} border-4 border-purple-200 border-t-purple-600 rounded-full animate-spin`}></div>
        <Sparkles className="w-3 h-3 text-purple-500 absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 animate-pulse" />
      </div>
      {message && (
        <p className="mt-3 text-sm text-gray-600 animate-pulse">{message}</p>
      )}
    </div>
  );
};

export default ModernLoadingSpinner;
