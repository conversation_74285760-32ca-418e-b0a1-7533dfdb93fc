import React, { useState, useEffect } from 'react';
import { Heart, Sparkles, Star, X } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { 
  addInterestTag, 
  removeInterestTag, 
  getUserTagForProfile, 
  TAG_TYPES, 
  TagType 
} from '../../lib/userTagging';
import { UserInterestTag } from '../../types';

interface InterestTagButtonProps {
  profileId: string;
  profileName: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
}

const InterestTagButton: React.FC<InterestTagButtonProps> = ({
  profileId,
  profileName,
  className = '',
  size = 'md'
}) => {
  const { user } = useAuth();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [currentTag, setCurrentTag] = useState<UserInterestTag | null>(null);
  const [showDropdown, setShowDropdown] = useState(false);
  const [loading, setLoading] = useState(false);

  // Size configurations
  const sizeConfig = {
    sm: {
      button: 'px-2 py-1 text-xs',
      icon: 'w-3 h-3',
      dropdown: 'text-xs'
    },
    md: {
      button: 'px-3 py-2 text-sm',
      icon: 'w-4 h-4',
      dropdown: 'text-sm'
    },
    lg: {
      button: 'px-4 py-2 text-base',
      icon: 'w-5 h-5',
      dropdown: 'text-base'
    }
  };

  const config = sizeConfig[size];

  // Load existing tag on mount
  useEffect(() => {
    if (user?.id && profileId) {
      loadExistingTag();
    }
  }, [user?.id, profileId]);

  const loadExistingTag = async () => {
    if (!user?.id) return;

    try {
      const tag = await getUserTagForProfile(profileId, user.id);
      setCurrentTag(tag);
    } catch (err) {
      console.error('Failed to load existing tag:', err);
      // Silently fail if tables don't exist yet
      setCurrentTag(null);
    }
  };

  const handleTagSelect = async (tagType: TagType) => {
    if (!user?.id || loading) return;

    setLoading(true);
    const loadingToastId = showLoading(`Adding ${TAG_TYPES[tagType].label} tag...`);

    try {
      const result = await addInterestTag(profileId, tagType, user.id);
      
      if (result.success && result.tag) {
        setCurrentTag(result.tag);
        showSuccess(`Tagged ${profileName} as ${TAG_TYPES[tagType].label}!`);
      } else {
        showError(result.error || 'Failed to add tag');
      }
    } catch (err) {
      showError('Failed to add tag');
    } finally {
      setLoading(false);
      dismiss(loadingToastId);
      setShowDropdown(false);
    }
  };

  const handleRemoveTag = async () => {
    if (!user?.id || loading) return;

    setLoading(true);
    const loadingToastId = showLoading('Removing tag...');

    try {
      const result = await removeInterestTag(profileId, user.id);
      
      if (result.success) {
        setCurrentTag(null);
        showSuccess('Tag removed');
      } else {
        showError(result.error || 'Failed to remove tag');
      }
    } catch (err) {
      showError('Failed to remove tag');
    } finally {
      setLoading(false);
      dismiss(loadingToastId);
      setShowDropdown(false);
    }
  };

  // Don't show for own profile
  if (user?.id === profileId) {
    return null;
  }

  const getTagIcon = (tagType: TagType) => {
    switch (tagType) {
      case 'interested':
        return <Heart className={config.icon} />;
      case 'very_interested':
        return <Sparkles className={config.icon} />;
      case 'potential_match':
        return <Star className={config.icon} />;
      default:
        return <Heart className={config.icon} />;
    }
  };

  const getButtonStyle = () => {
    if (currentTag) {
      const tagInfo = TAG_TYPES[currentTag.tag_type as TagType];
      return `bg-gradient-to-r from-purple-500 to-pink-500 text-white hover:from-purple-600 hover:to-pink-600`;
    }
    return `bg-white text-gray-700 border border-gray-300 hover:bg-gray-50`;
  };

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        disabled={loading}
        className={`
          ${config.button}
          ${getButtonStyle()}
          rounded-lg font-medium transition-all duration-200 
          flex items-center gap-2 shadow-sm
          ${loading ? 'opacity-50 cursor-not-allowed' : 'hover:shadow-md'}
        `}
      >
        {currentTag ? (
          <>
            <span>{TAG_TYPES[currentTag.tag_type as TagType].emoji}</span>
            <span>{TAG_TYPES[currentTag.tag_type as TagType].label}</span>
          </>
        ) : (
          <>
            <Heart className={config.icon} />
            <span>Tag Interest</span>
          </>
        )}
      </button>

      {showDropdown && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setShowDropdown(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-56 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
            <div className="p-2">
              <div className={`${config.dropdown} text-gray-500 px-3 py-2 font-medium`}>
                Express your interest:
              </div>
              
              {Object.entries(TAG_TYPES).map(([key, tagInfo]) => (
                <button
                  key={key}
                  onClick={() => handleTagSelect(key as TagType)}
                  disabled={loading}
                  className={`
                    w-full text-left px-3 py-2 rounded-md transition-colors
                    ${config.dropdown}
                    ${currentTag?.tag_type === key 
                      ? 'bg-purple-50 text-purple-700' 
                      : 'text-gray-700 hover:bg-gray-50'
                    }
                    ${loading ? 'opacity-50 cursor-not-allowed' : ''}
                  `}
                >
                  <div className="flex items-center gap-3">
                    <span className="text-lg">{tagInfo.emoji}</span>
                    <div>
                      <div className="font-medium">{tagInfo.label}</div>
                      <div className="text-xs text-gray-500">{tagInfo.description}</div>
                    </div>
                  </div>
                </button>
              ))}
              
              {currentTag && (
                <>
                  <div className="border-t border-gray-100 my-2" />
                  <button
                    onClick={handleRemoveTag}
                    disabled={loading}
                    className={`
                      w-full text-left px-3 py-2 rounded-md transition-colors
                      ${config.dropdown}
                      text-red-600 hover:bg-red-50
                      ${loading ? 'opacity-50 cursor-not-allowed' : ''}
                    `}
                  >
                    <div className="flex items-center gap-3">
                      <X className={config.icon} />
                      <span>Remove Tag</span>
                    </div>
                  </button>
                </>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default InterestTagButton;
