import { supabase } from '../lib/supabase';
import { <PERSON><PERSON><PERSON><PERSON>ogger } from '../lib/errorHandler';

/**
 * Setup database tables for conversation boundaries
 * This function attempts to create the necessary tables if they don't exist
 */
export async function setupConversationBoundariesTables(): Promise<{ success: boolean; error?: string }> {
  try {
    // Note: This approach uses RPC functions to execute SQL
    // In a production environment, you would typically run migrations through Supabase CLI
    
    const setupSQL = `
      -- Create conversation_boundaries table for detailed boundary settings
      CREATE TABLE IF NOT EXISTS conversation_boundaries (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
        
        -- Communication preferences
        allow_messages_from TEXT DEFAULT 'everyone' CHECK (allow_messages_from IN ('everyone', 'tagged_only', 'mutual_interest', 'none')),
        require_introduction BOOLEAN DEFAULT false,
        auto_decline_explicit BOOLEAN DEFAULT true,
        
        -- Response time expectations
        response_time_expectation TEXT DEFAULT 'flexible' CHECK (response_time_expectation IN ('immediate', 'within_hours', 'within_day', 'flexible')),
        availability_window_start TIME,
        availability_window_end TIME,
        availability_timezone TEXT DEFAULT 'UTC',
        
        -- Content filtering
        block_explicit_content BOOLEAN DEFAULT true,
        block_personal_questions_early BOOLEAN DEFAULT false,
        block_meeting_requests_early BOOLEAN DEFAULT false,
        block_contact_sharing_early BOOLEAN DEFAULT true,
        
        -- Custom boundaries (JSON array of strings)
        custom_boundaries JSONB DEFAULT '[]'::jsonb,
        
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now()
      );

      -- Create blocked_users table
      CREATE TABLE IF NOT EXISTS blocked_users (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        blocker_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
        blocked_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
        reason TEXT,
        created_at TIMESTAMPTZ DEFAULT now(),
        
        -- Ensure a user can't block the same person twice
        UNIQUE(blocker_id, blocked_id)
      );

      -- Create conversation_reports table for reporting inappropriate behavior
      CREATE TABLE IF NOT EXISTS conversation_reports (
        id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
        reporter_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
        reported_user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
        conversation_id uuid, -- Reference to conversation if applicable
        report_type TEXT NOT NULL CHECK (report_type IN ('harassment', 'inappropriate_content', 'spam', 'fake_profile', 'other')),
        description TEXT,
        status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
        created_at TIMESTAMPTZ DEFAULT now(),
        updated_at TIMESTAMPTZ DEFAULT now()
      );

      -- Enable RLS on all tables
      ALTER TABLE conversation_boundaries ENABLE ROW LEVEL SECURITY;
      ALTER TABLE blocked_users ENABLE ROW LEVEL SECURITY;
      ALTER TABLE conversation_reports ENABLE ROW LEVEL SECURITY;

      -- RLS Policies for conversation_boundaries
      DROP POLICY IF EXISTS "Users can view their own boundaries" ON conversation_boundaries;
      CREATE POLICY "Users can view their own boundaries" ON conversation_boundaries
        FOR SELECT USING (auth.uid()::text = user_id::text);

      DROP POLICY IF EXISTS "Users can insert their own boundaries" ON conversation_boundaries;
      CREATE POLICY "Users can insert their own boundaries" ON conversation_boundaries
        FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

      DROP POLICY IF EXISTS "Users can update their own boundaries" ON conversation_boundaries;
      CREATE POLICY "Users can update their own boundaries" ON conversation_boundaries
        FOR UPDATE USING (auth.uid()::text = user_id::text);

      DROP POLICY IF EXISTS "Users can delete their own boundaries" ON conversation_boundaries;
      CREATE POLICY "Users can delete their own boundaries" ON conversation_boundaries
        FOR DELETE USING (auth.uid()::text = user_id::text);

      -- RLS Policies for blocked_users
      DROP POLICY IF EXISTS "Users can view their blocked users" ON blocked_users;
      CREATE POLICY "Users can view their blocked users" ON blocked_users
        FOR SELECT USING (auth.uid()::text = blocker_id::text);

      DROP POLICY IF EXISTS "Users can block other users" ON blocked_users;
      CREATE POLICY "Users can block other users" ON blocked_users
        FOR INSERT WITH CHECK (auth.uid()::text = blocker_id::text);

      DROP POLICY IF EXISTS "Users can unblock users" ON blocked_users;
      CREATE POLICY "Users can unblock users" ON blocked_users
        FOR DELETE USING (auth.uid()::text = blocker_id::text);

      -- RLS Policies for conversation_reports
      DROP POLICY IF EXISTS "Users can view their own reports" ON conversation_reports;
      CREATE POLICY "Users can view their own reports" ON conversation_reports
        FOR SELECT USING (auth.uid()::text = reporter_id::text);

      DROP POLICY IF EXISTS "Users can create reports" ON conversation_reports;
      CREATE POLICY "Users can create reports" ON conversation_reports
        FOR INSERT WITH CHECK (auth.uid()::text = reporter_id::text);

      -- Create indexes for performance
      CREATE INDEX IF NOT EXISTS idx_conversation_boundaries_user_id ON conversation_boundaries(user_id);
      CREATE INDEX IF NOT EXISTS idx_blocked_users_blocker_id ON blocked_users(blocker_id);
      CREATE INDEX IF NOT EXISTS idx_blocked_users_blocked_id ON blocked_users(blocked_id);
      CREATE INDEX IF NOT EXISTS idx_conversation_reports_reporter_id ON conversation_reports(reporter_id);
      CREATE INDEX IF NOT EXISTS idx_conversation_reports_reported_user_id ON conversation_reports(reported_user_id);
    `;

    // Execute the setup SQL
    const { error } = await supabase.rpc('exec_sql', { sql: setupSQL });
    
    if (error) {
      ErrorLogger.logError(error, 'database');
      return { 
        success: false, 
        error: 'Failed to setup database tables. Please run the migration manually.' 
      };
    }

    return { success: true };
  } catch (err) {
    ErrorLogger.logError(err, 'database');
    return { 
      success: false, 
      error: 'An error occurred while setting up the database tables.' 
    };
  }
}

/**
 * Check if conversation boundaries tables exist
 */
export async function checkTablesExist(): Promise<boolean> {
  try {
    // Try to query each table to see if it exists
    const { error: boundariesError } = await supabase
      .from('conversation_boundaries')
      .select('count', { count: 'exact', head: true });

    const { error: blockedError } = await supabase
      .from('blocked_users')
      .select('count', { count: 'exact', head: true });

    const { error: reportsError } = await supabase
      .from('conversation_reports')
      .select('count', { count: 'exact', head: true });

    // If any table doesn't exist, return false
    if (boundariesError || blockedError || reportsError) {
      return false;
    }

    return true;
  } catch (err) {
    return false;
  }
}
