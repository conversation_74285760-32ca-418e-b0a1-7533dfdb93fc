-- ====================================================================
-- Conversation Boundaries Management System Setup
-- ====================================================================
-- This script sets up the conversation boundaries system including
-- blocked users, communication preferences, and reporting functionality.

-- Create conversation_boundaries table for detailed boundary settings
CREATE TABLE IF NOT EXISTS conversation_boundaries (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  
  -- Communication preferences
  allow_messages_from TEXT DEFAULT 'everyone' CHECK (allow_messages_from IN ('everyone', 'tagged_only', 'mutual_interest', 'none')),
  require_introduction BOOLEAN DEFAULT false,
  auto_decline_explicit BOOLEAN DEFAULT true,
  
  -- Response time expectations
  response_time_expectation TEXT DEFAULT 'flexible' CHECK (response_time_expectation IN ('immediate', 'within_hours', 'within_day', 'flexible')),
  availability_window_start TIME,
  availability_window_end TIME,
  availability_timezone TEXT DEFAULT 'UTC',
  
  -- Content filtering
  block_explicit_content BOOLEAN DEFAULT true,
  block_personal_questions_early BOOLEAN DEFAULT false,
  block_meeting_requests_early BOOLEAN DEFAULT false,
  block_contact_sharing_early BOOLEAN DEFAULT true,
  
  -- Custom boundaries (JSON array of strings)
  custom_boundaries JSONB DEFAULT '[]'::jsonb,
  
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Create blocked_users table
CREATE TABLE IF NOT EXISTS blocked_users (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  blocker_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  blocked_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  reason TEXT,
  created_at TIMESTAMPTZ DEFAULT now(),
  
  -- Ensure a user can't block the same person twice
  UNIQUE(blocker_id, blocked_id)
);

-- Create conversation_reports table for reporting inappropriate behavior
CREATE TABLE IF NOT EXISTS conversation_reports (
  id uuid PRIMARY KEY DEFAULT gen_random_uuid(),
  reporter_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  reported_user_id uuid REFERENCES profiles(id) ON DELETE CASCADE NOT NULL,
  conversation_id uuid, -- Reference to conversation if applicable
  report_type TEXT NOT NULL CHECK (report_type IN ('harassment', 'inappropriate_content', 'spam', 'fake_profile', 'other')),
  description TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'reviewed', 'resolved', 'dismissed')),
  created_at TIMESTAMPTZ DEFAULT now(),
  updated_at TIMESTAMPTZ DEFAULT now()
);

-- Enable RLS on all tables
ALTER TABLE conversation_boundaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE blocked_users ENABLE ROW LEVEL SECURITY;
ALTER TABLE conversation_reports ENABLE ROW LEVEL SECURITY;

-- RLS Policies for conversation_boundaries
CREATE POLICY "Users can view their own boundaries" ON conversation_boundaries
  FOR SELECT USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can insert their own boundaries" ON conversation_boundaries
  FOR INSERT WITH CHECK (auth.uid()::text = user_id::text);

CREATE POLICY "Users can update their own boundaries" ON conversation_boundaries
  FOR UPDATE USING (auth.uid()::text = user_id::text);

CREATE POLICY "Users can delete their own boundaries" ON conversation_boundaries
  FOR DELETE USING (auth.uid()::text = user_id::text);

-- RLS Policies for blocked_users
CREATE POLICY "Users can view their blocked users" ON blocked_users
  FOR SELECT USING (auth.uid()::text = blocker_id::text);

CREATE POLICY "Users can block other users" ON blocked_users
  FOR INSERT WITH CHECK (auth.uid()::text = blocker_id::text);

CREATE POLICY "Users can unblock users" ON blocked_users
  FOR DELETE USING (auth.uid()::text = blocker_id::text);

-- RLS Policies for conversation_reports
CREATE POLICY "Users can view their own reports" ON conversation_reports
  FOR SELECT USING (auth.uid()::text = reporter_id::text);

CREATE POLICY "Users can create reports" ON conversation_reports
  FOR INSERT WITH CHECK (auth.uid()::text = reporter_id::text);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_conversation_boundaries_user_id ON conversation_boundaries(user_id);
CREATE INDEX IF NOT EXISTS idx_blocked_users_blocker_id ON blocked_users(blocker_id);
CREATE INDEX IF NOT EXISTS idx_blocked_users_blocked_id ON blocked_users(blocked_id);
CREATE INDEX IF NOT EXISTS idx_conversation_reports_reporter_id ON conversation_reports(reporter_id);
CREATE INDEX IF NOT EXISTS idx_conversation_reports_reported_user_id ON conversation_reports(reported_user_id);

-- Create function to update updated_at timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = now();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Create triggers for updated_at
CREATE TRIGGER update_conversation_boundaries_updated_at 
  BEFORE UPDATE ON conversation_boundaries 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_conversation_reports_updated_at 
  BEFORE UPDATE ON conversation_reports 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Insert default boundaries for existing users
INSERT INTO conversation_boundaries (user_id)
SELECT id FROM profiles 
WHERE id NOT IN (SELECT user_id FROM conversation_boundaries WHERE user_id IS NOT NULL)
ON CONFLICT DO NOTHING;

-- Success message
SELECT 'Conversation boundaries system setup completed successfully!' as status;
