#!/usr/bin/env node

/**
 * Test script for personality system functionality
 * Tests the complete personality test flow and data integration
 */

import { createClient } from '@supabase/supabase-js';
import { readFileSync } from 'fs';

// Read environment variables from .env file manually
let supabaseUrl, supabaseKey;
try {
  const envContent = readFileSync('.env', 'utf8');
  const envLines = envContent.split('\n');

  for (const line of envLines) {
    if (line.startsWith('VITE_SUPABASE_URL=')) {
      supabaseUrl = line.split('=')[1].trim();
    }
    if (line.startsWith('VITE_SUPABASE_ANON_KEY=')) {
      supabaseKey = line.split('=')[1].trim();
    }
  }
} catch (error) {
  console.log('⚠️ Could not read .env file, using process.env');
  supabaseUrl = process.env.VITE_SUPABASE_URL;
  supabaseKey = process.env.VITE_SUPABASE_ANON_KEY;
}

if (!supabaseUrl || !supabaseKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

async function testPersonalitySystem() {
  console.log('🧠 Testing Personality System...\n');

  try {
    // Test 1: Check if personality columns exist in profiles table
    console.log('1️⃣ Testing database schema...');
    const { data: schemaTest, error: schemaError } = await supabase
      .from('profiles')
      .select('extraversion_score, agreeableness_score, conscientiousness_score, neuroticism_score, openness_score, personality_test_completed_at, personality_insights')
      .limit(1);

    if (schemaError) {
      console.error('❌ Schema test failed:', schemaError.message);
      return false;
    }
    console.log('✅ Database schema is correct');

    // Test 2: Test personality data update
    console.log('\n2️⃣ Testing personality data update...');
    
    // Get current user (if authenticated)
    const { data: { user }, error: authError } = await supabase.auth.getUser();
    
    if (authError || !user) {
      console.log('⚠️ No authenticated user found. Testing with mock data...');
      
      // Test with a known test profile if it exists
      const { data: testProfile } = await supabase
        .from('profiles')
        .select('id')
        .eq('username', 'roberto_test1')
        .single();

      if (testProfile) {
        console.log('📝 Testing personality update with test profile...');
        
        const testPersonalityData = {
          extraversion_score: 75,
          agreeableness_score: 80,
          conscientiousness_score: 70,
          neuroticism_score: 30,
          openness_score: 85,
          personality_test_completed_at: new Date().toISOString(),
          personality_insights: {
            overallProfile: "Test personality profile",
            strengths: ["Creative", "Analytical"],
            growthAreas: ["Time management"],
            communicationStyle: ["Direct", "Thoughtful"],
            relationshipTendencies: ["Values deep connections"],
            testDate: new Date().toISOString(),
            bigFiveScores: {
              extraversion: 75,
              openness: 85,
              conscientiousness: 70,
              agreeableness: 80,
              neuroticism: 30
            }
          }
        };

        const { error: updateError } = await supabase
          .from('profiles')
          .update(testPersonalityData)
          .eq('id', testProfile.id);

        if (updateError) {
          console.error('❌ Personality update failed:', updateError.message);
          return false;
        }
        console.log('✅ Personality data update successful');

        // Test 3: Verify data retrieval
        console.log('\n3️⃣ Testing personality data retrieval...');
        const { data: retrievedData, error: retrieveError } = await supabase
          .from('profiles')
          .select('extraversion_score, agreeableness_score, conscientiousness_score, neuroticism_score, openness_score, personality_insights')
          .eq('id', testProfile.id)
          .single();

        if (retrieveError) {
          console.error('❌ Data retrieval failed:', retrieveError.message);
          return false;
        }

        console.log('✅ Personality data retrieved successfully:');
        console.log('   - Extraversion:', retrievedData.extraversion_score);
        console.log('   - Agreeableness:', retrievedData.agreeableness_score);
        console.log('   - Conscientiousness:', retrievedData.conscientiousness_score);
        console.log('   - Neuroticism:', retrievedData.neuroticism_score);
        console.log('   - Openness:', retrievedData.openness_score);
        console.log('   - Insights available:', !!retrievedData.personality_insights);
      }
    } else {
      console.log('✅ Authenticated user found:', user.id);
    }

    // Test 4: Test personality compatibility calculation
    console.log('\n4️⃣ Testing personality compatibility calculation...');
    
    const user1Scores = { extraversion_score: 75, agreeableness_score: 80, conscientiousness_score: 70, neuroticism_score: 30, openness_score: 85 };
    const user2Scores = { extraversion_score: 65, agreeableness_score: 85, conscientiousness_score: 75, neuroticism_score: 25, openness_score: 80 };
    
    // Simple compatibility calculation
    const traits = ['extraversion_score', 'agreeableness_score', 'conscientiousness_score', 'neuroticism_score', 'openness_score'];
    let totalDifference = 0;
    let validTraits = 0;

    traits.forEach(trait => {
      const score1 = user1Scores[trait] || 0;
      const score2 = user2Scores[trait] || 0;
      
      if (score1 > 0 && score2 > 0) {
        totalDifference += Math.abs(score1 - score2);
        validTraits++;
      }
    });

    const compatibility = validTraits > 0 ? Math.max(0, 100 - (totalDifference / validTraits)) : 0;
    console.log('✅ Compatibility calculation successful:', Math.round(compatibility) + '%');

    console.log('\n🎉 All personality system tests passed!');
    return true;

  } catch (error) {
    console.error('❌ Test failed with error:', error.message);
    return false;
  }
}

// Run the test
testPersonalitySystem()
  .then(success => {
    if (success) {
      console.log('\n✅ Personality system is working correctly!');
      process.exit(0);
    } else {
      console.log('\n❌ Personality system has issues that need to be fixed.');
      process.exit(1);
    }
  })
  .catch(error => {
    console.error('❌ Test script failed:', error);
    process.exit(1);
  });
