import React, { useEffect, useState } from 'react';
import { Link, useNavigate } from 'react-router-dom';
import { Plus, User, MessageCircle, Bar<PERSON><PERSON>, ThumbsUp } from 'lucide-react';
import { useProfile } from '../context/ProfileContext';
import { useConversation } from '../context/ConversationContext';
import { useToast } from '../hooks/useToast';
import { DigitalTwin, Profile } from '../types';
import { SuggestedMatches } from '../components/dashboard/SuggestedMatches';
import { RecentConversations } from '../components/dashboard/RecentConversations';
import { EngagementStats } from '../components/dashboard/EngagementStats';
import { UserSearch } from '../components/search/UserSearch';
import DetailedProfileView from '../components/discovery/DetailedProfileView';
import { getTwins } from '../utils/twinService';
import { LoadingSpinner } from '../components/ui/LoadingSpinner';

const Dashboard: React.FC = () => {
  const { profile } = useProfile();
  const { conversations, startConversation } = useConversation();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const navigate = useNavigate();
  const [suggestedTwins, setSuggestedTwins] = useState<DigitalTwin[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedProfile, setSelectedProfile] = useState<Profile | null>(null);
  const [isDetailViewOpen, setIsDetailViewOpen] = useState(false);

  useEffect(() => {
    const loadTwins = async () => {
      try {
        const twins = getTwins();
        setSuggestedTwins(twins);
      } catch (error) {
        console.error('Error loading twins:', error);
      } finally {
        setIsLoading(false);
      }
    };

    loadTwins();
  }, []);

  const handleUserSelect = (user: Profile) => {
    setSelectedProfile(user);
    setIsDetailViewOpen(true);
  };

  const handleCloseDetailView = () => {
    setIsDetailViewOpen(false);
    setSelectedProfile(null);
  };

  const handleStartChat = async (profileToChat: Profile) => {
    const loadingToastId = showLoading(`Starting chat with ${profileToChat.name}...`);
    try {
      const newConversation = await startConversation(profileToChat, 'real_user');
      dismiss(loadingToastId);
      if (newConversation && newConversation.id) {
        showSuccess(`Chat started with ${profileToChat.name}!`);
        navigate(`/conversations/${newConversation.id}`);
      } else {
        showError(`Failed to start chat with ${profileToChat.name}.`);
      }
    } catch (err) {
      dismiss(loadingToastId);
      showError(`Error starting chat: ${err instanceof Error ? err.message : 'Unknown error'}`);
      console.error("Error starting chat:", err);
    }
    handleCloseDetailView();
  };

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <LoadingSpinner size="lg" />
      </div>
    );
  }

  if (!profile) {
    return (
      <div className="container mx-auto px-4 py-12">
        <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-md overflow-hidden">
          <div className="p-8 text-center">
            <User className="w-16 h-16 mx-auto text-purple-500 mb-4" />
            <h1 className="text-2xl font-bold text-gray-800 mb-4">Welcome to SoulTwinSync</h1>
            <p className="text-gray-600 mb-8">
              Create your profile to start matching with digital twins that match your personality and preferences.
            </p>
            <Link 
              to="/profile/create" 
              className="inline-flex items-center px-6 py-3 bg-gradient-to-r from-purple-600 to-pink-500 text-white font-semibold modern-btn shadow-md hover:from-purple-700 hover:to-pink-600 transition duration-300"
            >
              <Plus className="w-5 h-5 mr-2" />
              Create Your Profile
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="modern-card fade-in bg-white rounded-xl shadow-md p-6 mb-8">
        <div className="flex flex-col md:flex-row items-start md:items-center">
          <div className="flex-shrink-0 mb-4 md:mb-0 md:mr-6">
            {profile.avatar_url ? (
              <img
                src={profile.avatar_url}
                alt={profile.name}
                className="w-20 h-20 rounded-full object-cover"
              />
            ) : (
              <div className="w-20 h-20 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-2xl font-bold">
                {profile.name?.charAt(0) || '?'}
              </div>
            )}
          </div>
          <div>
            <h1 className="text-2xl font-bold text-gray-800">Welcome back, {profile.name}!</h1>
            <p className="text-gray-600 mt-1">
              You have {conversations?.length || 0} active conversations and {suggestedTwins.length} new suggested matches.
            </p>
          </div>
          <div className="mt-4 md:mt-0 md:ml-auto">
            <Link 
              to="/conversations" 
              className="inline-flex items-center px-5 py-2 bg-purple-600 text-white font-medium rounded-lg modern-btn hover:bg-purple-700 transition duration-200"
            >
              <MessageCircle className="w-5 h-5 mr-2" />
              View Conversations
            </Link>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        <div className="lg:col-span-2">
          <div className="modern-card fade-in bg-white rounded-xl shadow-md p-6 mb-8">            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              <User className="w-5 h-5 text-purple-500 mr-2" />
              Find Users
            </h2>
            <UserSearch onUserSelect={handleUserSelect} />
          </div>
          
          <div className="modern-card fade-in bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              <ThumbsUp className="w-5 h-5 text-purple-500 mr-2" />
              Suggested Matches
            </h2>
            <SuggestedMatches suggestedTwins={suggestedTwins} />
          </div>
          
          <div className="modern-card fade-in bg-white rounded-xl shadow-md p-6">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              <MessageCircle className="w-5 h-5 text-purple-500 mr-2" />
              Recent Conversations
            </h2>
            <RecentConversations conversations={conversations || []} />
          </div>
        </div>
        
        <div>
          <div className="modern-card fade-in bg-white rounded-xl shadow-md p-6 mb-8">
            <h2 className="text-xl font-semibold text-gray-800 mb-4 flex items-center">
              <BarChart className="w-5 h-5 text-purple-500 mr-2" />
              Engagement Stats
            </h2>
            <EngagementStats conversations={conversations || []} />
          </div>
          
          <div className="bg-gradient-to-r from-purple-600 to-pink-500 rounded-xl shadow-md p-6 text-white modern-card fade-in">
            <h2 className="text-xl font-semibold mb-4">Quick Tips</h2>
            <ul className="space-y-3">
              <li className="flex items-start">
                <span className="flex-shrink-0 w-5 h-5 bg-white text-purple-600 rounded-full flex items-center justify-center text-xs font-bold mr-2 mt-0.5">1</span>
                <span>Ask open-ended questions to keep the conversation flowing</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-5 h-5 bg-white text-purple-600 rounded-full flex items-center justify-center text-xs font-bold mr-2 mt-0.5">2</span>
                <span>Share personal stories to create connection</span>
              </li>
              <li className="flex items-start">
                <span className="flex-shrink-0 w-5 h-5 bg-white text-purple-600 rounded-full flex items-center justify-center text-xs font-bold mr-2 mt-0.5">3</span>
                <span>Move to real-world meetup when you feel comfortable</span>
              </li>
            </ul>          </div>
        </div>
      </div>

      {/* Profile Detail Modal */}
      {isDetailViewOpen && selectedProfile && (
        <DetailedProfileView
          profile={selectedProfile}
          onStartChat={handleStartChat}
          onClose={handleCloseDetailView}
        />
      )}
    </div>
  );
};

export default Dashboard;