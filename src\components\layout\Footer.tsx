import React from 'react';
import { Link } from 'react-router-dom';
import { Heart, Mail, Shield } from 'lucide-react';

export const Footer: React.FC = () => {
  return (
    <footer className="bg-gray-900/80 backdrop-blur-md shadow-2xl text-gray-300 rounded-t-2xl">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
          <div>
            <div className="flex items-center space-x-2 mb-4">
              <Heart className="h-6 w-6 text-pink-500" />
              <span className="text-xl font-bold text-white">SoulTwinSync</span>
            </div>
            <p className="text-sm text-gray-400 mb-4">
              Connect with AI companions that understand your personality and communication style. 
              Experience meaningful conversations that lead to deeper self-understanding.
            </p>
          </div>
          
          <div>
            <h3 className="text-white font-semibold mb-4">Quick Links</h3>
            <ul className="space-y-2 text-sm">
              <li>
                <Link to="/dashboard" className="hover:text-pink-400 transition duration-200">
                  Dashboard
                </Link>
              </li>
              <li>
                <Link to="/conversations" className="hover:text-pink-400 transition duration-200">
                  Conversations
                </Link>
              </li>
              <li>
                <Link to="/settings" className="hover:text-pink-400 transition duration-200">
                  Settings
                </Link>
              </li>
              <li>
                <Link to="/privacy" className="hover:text-pink-400 transition duration-200">
                  Privacy Policy
                </Link>
              </li>
            </ul>
          </div>
          
          <div>
            <h3 className="text-white font-semibold mb-4">Contact & Support</h3>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center space-x-2">
                <Mail className="h-4 w-4 text-gray-400" />
                <a href="mailto:<EMAIL>" className="hover:text-pink-400 transition duration-200">
                  <EMAIL>
                </a>
              </li>
              <li className="flex items-center space-x-2">
                <Shield className="h-4 w-4 text-gray-400" />
                <Link to="/privacy" className="hover:text-pink-400 transition duration-200">
                  Privacy & Safety
                </Link>
              </li>
            </ul>
          </div>
        </div>
        
        <div className="border-t border-gray-800 mt-12 pt-6 text-center text-xs text-gray-500">
          <p>© {new Date().getFullYear()} SoulTwinSync. All rights reserved.</p>
        </div>
      </div>
    </footer>
  );
};