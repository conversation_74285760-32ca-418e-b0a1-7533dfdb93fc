import { Toaster } from 'react-hot-toast';

export const Toast = () => {
  return (
    <Toaster
      position="top-right"
      toastOptions={{
        duration: 4000,
        style: {
          background: 'rgba(40,40,60,0.85)',
          color: '#fff',
          borderRadius: '1rem',
          boxShadow: '0 4px 32px 0 rgba(80, 0, 120, 0.10)',
          backdropFilter: 'blur(8px) saturate(180%)',
          WebkitBackdropFilter: 'blur(8px) saturate(180%)',
          fontWeight: 500,
          fontSize: '1rem',
          animation: 'fadeIn 0.4s cubic-bezier(0.4,0,0.2,1)'
        },
        success: {
          duration: 3000,
          iconTheme: {
            primary: '#4ade80',
            secondary: '#fff',
          },
        },
        error: {
          duration: 4000,
          iconTheme: {
            primary: '#ef4444',
            secondary: '#fff',
          },
        },
      }}
    />
  );
};