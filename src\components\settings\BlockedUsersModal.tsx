import React, { useState, useEffect } from 'react';
import { X, UserX, Search, AlertCircle } from 'lucide-react';
import { BlockedUser } from '../../types';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { getBlockedUsers, unblockUser } from '../../lib/conversationBoundaries';
import { LoadingSpinner } from '../ui/LoadingSpinner';
import ConversationBoundariesSetup from './ConversationBoundariesSetup';

interface BlockedUsersModalProps {
  isOpen: boolean;
  onClose: () => void;
}

const BlockedUsersModal: React.FC<BlockedUsersModalProps> = ({
  isOpen,
  onClose
}) => {
  const { user } = useAuth();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [blockedUsers, setBlockedUsers] = useState<BlockedUser[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [tablesExist, setTablesExist] = useState(true);

  useEffect(() => {
    if (isOpen && user?.id) {
      loadBlockedUsers();
    }
  }, [isOpen, user?.id]);

  const loadBlockedUsers = async () => {
    if (!user?.id) return;

    setLoading(true);
    try {
      const data = await getBlockedUsers(user.id);
      setBlockedUsers(data);
      setTablesExist(true);
    } catch (err) {
      showError('Failed to load blocked users');
      setTablesExist(false);
    } finally {
      setLoading(false);
    }
  };

  const handleUnblock = async (blockedUserId: string, userName: string) => {
    if (!user?.id) return;

    const loadingToastId = showLoading(`Unblocking ${userName}...`);

    try {
      const result = await unblockUser(user.id, blockedUserId);
      
      if (result.success) {
        showSuccess(`${userName} has been unblocked`);
        setBlockedUsers(prev => prev.filter(bu => bu.blocked_id !== blockedUserId));
      } else {
        showError(result.error || 'Failed to unblock user');
      }
    } catch (err) {
      showError('Failed to unblock user');
    } finally {
      dismiss(loadingToastId);
    }
  };

  const filteredUsers = blockedUsers.filter(blockedUser =>
    blockedUser.blocked_user?.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
    blockedUser.blocked_user?.username?.toLowerCase().includes(searchTerm.toLowerCase())
  );

  if (!isOpen) return null;

  // Show setup component if tables don't exist
  if (!loading && !tablesExist) {
    return <ConversationBoundariesSetup onClose={onClose} />;
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b border-gray-200">
          <div className="flex items-center">
            <UserX className="w-6 h-6 text-red-500 mr-3" />
            <h2 className="text-xl font-semibold text-gray-800">Blocked Users</h2>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <X className="w-6 h-6" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <LoadingSpinner size="lg" />
            </div>
          ) : (
            <>
              {/* Search */}
              {blockedUsers.length > 0 && (
                <div className="mb-6">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-5 h-5" />
                    <input
                      type="text"
                      placeholder="Search blocked users..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                    />
                  </div>
                </div>
              )}

              {/* Blocked Users List */}
              {blockedUsers.length === 0 ? (
                <div className="text-center py-8">
                  <UserX className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-800 mb-2">No Blocked Users</h3>
                  <p className="text-gray-600">You haven't blocked any users yet.</p>
                </div>
              ) : filteredUsers.length === 0 ? (
                <div className="text-center py-8">
                  <Search className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-800 mb-2">No Results</h3>
                  <p className="text-gray-600">No blocked users match your search.</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredUsers.map((blockedUser) => (
                    <div
                      key={blockedUser.id}
                      className="flex items-center justify-between p-4 bg-gray-50 rounded-lg border border-gray-200"
                    >
                      <div className="flex items-center">
                        {/* Avatar */}
                        <div className="w-12 h-12 rounded-full bg-gradient-to-r from-purple-500 to-pink-500 flex items-center justify-center text-white text-lg font-bold mr-4">
                          {blockedUser.blocked_user?.avatar_url ? (
                            <img
                              src={blockedUser.blocked_user.avatar_url}
                              alt={blockedUser.blocked_user.name}
                              className="w-12 h-12 rounded-full object-cover"
                            />
                          ) : (
                            blockedUser.blocked_user?.name?.charAt(0) || '?'
                          )}
                        </div>

                        {/* User Info */}
                        <div>
                          <h4 className="font-medium text-gray-800">
                            {blockedUser.blocked_user?.name || 'Unknown User'}
                          </h4>
                          {blockedUser.blocked_user?.username && (
                            <p className="text-sm text-gray-600">@{blockedUser.blocked_user.username}</p>
                          )}
                          {blockedUser.reason && (
                            <p className="text-sm text-gray-500 mt-1">
                              <AlertCircle className="w-3 h-3 inline mr-1" />
                              {blockedUser.reason}
                            </p>
                          )}
                          <p className="text-xs text-gray-400 mt-1">
                            Blocked on {new Date(blockedUser.created_at).toLocaleDateString()}
                          </p>
                        </div>
                      </div>

                      {/* Unblock Button */}
                      <button
                        onClick={() => handleUnblock(
                          blockedUser.blocked_id,
                          blockedUser.blocked_user?.name || 'this user'
                        )}
                        className="px-4 py-2 bg-red-600 text-white rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                      >
                        Unblock
                      </button>
                    </div>
                  ))}
                </div>
              )}

              {/* Info Box */}
              <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                <div className="flex items-start">
                  <AlertCircle className="w-5 h-5 text-blue-500 mr-2 mt-0.5 flex-shrink-0" />
                  <div className="text-sm text-blue-800">
                    <p className="font-medium mb-1">About Blocking Users</p>
                    <ul className="space-y-1 text-blue-700">
                      <li>• Blocked users cannot send you messages or see your profile</li>
                      <li>• You won't see their profiles in discovery or receive notifications from them</li>
                      <li>• Existing conversations will be hidden but not deleted</li>
                      <li>• You can unblock users at any time</li>
                    </ul>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end p-6 border-t border-gray-200">
          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default BlockedUsersModal;
