import React, { useState } from 'react';
import { UserX, AlertTriangle, Flag } from 'lucide-react';
import { useAuth } from '../../hooks/useAuth';
import { useToast } from '../../hooks/useToast';
import { blockUser, reportUser } from '../../lib/conversationBoundaries';
import { ConversationReport } from '../../types';

interface BlockUserButtonProps {
  userId: string;
  userName: string;
  className?: string;
  size?: 'sm' | 'md' | 'lg';
  variant?: 'button' | 'dropdown';
}

const BlockUserButton: React.FC<BlockUserButtonProps> = ({
  userId,
  userName,
  className = '',
  size = 'md',
  variant = 'button'
}) => {
  const { user } = useAuth();
  const { showSuccess, showError, showLoading, dismiss } = useToast();
  const [showDropdown, setShowDropdown] = useState(false);
  const [showReportModal, setShowReportModal] = useState(false);
  const [reportType, setReportType] = useState<ConversationReport['report_type']>('harassment');
  const [reportDescription, setReportDescription] = useState('');
  const [loading, setLoading] = useState(false);

  const sizeConfig = {
    sm: { button: 'px-2 py-1 text-xs', icon: 'w-3 h-3' },
    md: { button: 'px-3 py-2 text-sm', icon: 'w-4 h-4' },
    lg: { button: 'px-4 py-2 text-base', icon: 'w-5 h-5' }
  };

  const config = sizeConfig[size];

  const handleBlock = async (reason?: string) => {
    if (!user?.id || loading) return;

    setLoading(true);
    const loadingToastId = showLoading(`Blocking ${userName}...`);

    try {
      const result = await blockUser(user.id, userId, reason);
      
      if (result.success) {
        showSuccess(`${userName} has been blocked`);
        setShowDropdown(false);
      } else {
        showError(result.error || 'Failed to block user');
      }
    } catch (err) {
      showError('Failed to block user');
    } finally {
      setLoading(false);
      dismiss(loadingToastId);
    }
  };

  const handleReport = async () => {
    if (!user?.id || loading) return;

    setLoading(true);
    const loadingToastId = showLoading('Submitting report...');

    try {
      const result = await reportUser(
        user.id,
        userId,
        reportType,
        reportDescription
      );
      
      if (result.success) {
        showSuccess('Report submitted successfully');
        setShowReportModal(false);
        setReportDescription('');
      } else {
        showError(result.error || 'Failed to submit report');
      }
    } catch (err) {
      showError('Failed to submit report');
    } finally {
      setLoading(false);
      dismiss(loadingToastId);
    }
  };

  // Don't show for own profile
  if (user?.id === userId) {
    return null;
  }

  if (variant === 'button') {
    return (
      <button
        onClick={() => handleBlock()}
        disabled={loading}
        className={`
          ${config.button}
          bg-red-600 text-white rounded-lg hover:bg-red-700 
          disabled:opacity-50 disabled:cursor-not-allowed
          transition-colors flex items-center gap-2
          ${className}
        `}
      >
        <UserX className={config.icon} />
        <span>Block</span>
      </button>
    );
  }

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setShowDropdown(!showDropdown)}
        disabled={loading}
        className={`
          ${config.button}
          bg-red-600 text-white rounded-lg hover:bg-red-700 
          disabled:opacity-50 disabled:cursor-not-allowed
          transition-colors flex items-center gap-2
        `}
      >
        <UserX className={config.icon} />
        <span>Block/Report</span>
      </button>

      {showDropdown && (
        <>
          {/* Backdrop */}
          <div 
            className="fixed inset-0 z-10" 
            onClick={() => setShowDropdown(false)}
          />
          
          {/* Dropdown */}
          <div className="absolute top-full left-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-gray-200 z-20">
            <div className="p-2">
              <button
                onClick={() => handleBlock()}
                disabled={loading}
                className="w-full text-left px-3 py-2 rounded-md transition-colors text-red-600 hover:bg-red-50 flex items-center gap-2"
              >
                <UserX className="w-4 h-4" />
                <span>Block User</span>
              </button>
              
              <button
                onClick={() => {
                  setShowReportModal(true);
                  setShowDropdown(false);
                }}
                disabled={loading}
                className="w-full text-left px-3 py-2 rounded-md transition-colors text-orange-600 hover:bg-orange-50 flex items-center gap-2"
              >
                <Flag className="w-4 h-4" />
                <span>Report User</span>
              </button>
            </div>
          </div>
        </>
      )}

      {/* Report Modal */}
      {showReportModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-xl shadow-xl max-w-md w-full">
            <div className="p-6">
              <div className="flex items-center mb-4">
                <AlertTriangle className="w-6 h-6 text-orange-500 mr-3" />
                <h3 className="text-lg font-semibold text-gray-800">Report {userName}</h3>
              </div>

              <div className="space-y-4">
                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Reason for reporting
                  </label>
                  <select
                    value={reportType}
                    onChange={(e) => setReportType(e.target.value as ConversationReport['report_type'])}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  >
                    <option value="harassment">Harassment</option>
                    <option value="inappropriate_content">Inappropriate Content</option>
                    <option value="spam">Spam</option>
                    <option value="fake_profile">Fake Profile</option>
                    <option value="other">Other</option>
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium text-gray-700 mb-2">
                    Additional details (optional)
                  </label>
                  <textarea
                    value={reportDescription}
                    onChange={(e) => setReportDescription(e.target.value)}
                    placeholder="Provide more details about the issue..."
                    rows={3}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-purple-500"
                  />
                </div>
              </div>

              <div className="flex items-center justify-end gap-3 mt-6">
                <button
                  onClick={() => setShowReportModal(false)}
                  className="px-4 py-2 text-gray-600 hover:text-gray-800 transition-colors"
                >
                  Cancel
                </button>
                <button
                  onClick={handleReport}
                  disabled={loading}
                  className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
                >
                  Submit Report
                </button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default BlockUserButton;
