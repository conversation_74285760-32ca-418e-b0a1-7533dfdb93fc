import React, { ReactNode } from 'react';
import { Search, Heart, Users, MessageCircle, Settings } from 'lucide-react';
import ModernButton from './ModernButton';

interface EmptyStateProps {
  type?: 'search' | 'matches' | 'conversations' | 'general';
  title?: string;
  description?: string;
  actionLabel?: string;
  onAction?: () => void;
  icon?: ReactNode;
  className?: string;
}

const EmptyState: React.FC<EmptyStateProps> = ({
  type = 'general',
  title,
  description,
  actionLabel,
  onAction,
  icon,
  className = ''
}) => {
  const getDefaultContent = () => {
    switch (type) {
      case 'search':
        return {
          icon: <Search className="w-16 h-16 text-gray-300" />,
          title: 'No matches found',
          description: 'Try adjusting your search filters or expanding your criteria to find more compatible matches.',
          actionLabel: 'Clear Filters'
        };
      case 'matches':
        return {
          icon: <Heart className="w-16 h-16 text-gray-300" />,
          title: 'No matches yet',
          description: 'Complete your personality assessment and add more photos to start discovering amazing people.',
          actionLabel: 'Complete Profile'
        };
      case 'conversations':
        return {
          icon: <MessageCircle className="w-16 h-16 text-gray-300" />,
          title: 'No conversations yet',
          description: 'Start connecting with people you like. Send a message to begin meaningful conversations.',
          actionLabel: 'Discover People'
        };
      default:
        return {
          icon: <Users className="w-16 h-16 text-gray-300" />,
          title: 'Nothing here yet',
          description: 'This section is empty. Check back later or explore other features.',
          actionLabel: 'Explore'
        };
    }
  };

  const defaultContent = getDefaultContent();
  const finalIcon = icon || defaultContent.icon;
  const finalTitle = title || defaultContent.title;
  const finalDescription = description || defaultContent.description;
  const finalActionLabel = actionLabel || defaultContent.actionLabel;

  return (
    <div className={`flex flex-col items-center justify-center py-16 px-6 text-center ${className}`}>
      {/* Decorative background */}
      <div className="relative mb-8">
        <div className="absolute inset-0 bg-gradient-to-r from-purple-100 to-pink-100 rounded-full blur-3xl opacity-50 scale-150"></div>
        <div className="relative bg-white rounded-full p-6 shadow-lg">
          {finalIcon}
        </div>
      </div>

      {/* Content */}
      <div className="max-w-md space-y-4">
        <h3 className="text-2xl font-bold text-gray-800">
          {finalTitle}
        </h3>
        <p className="text-gray-600 leading-relaxed">
          {finalDescription}
        </p>
      </div>

      {/* Action button */}
      {onAction && (
        <div className="mt-8">
          <ModernButton
            variant="gradient"
            size="lg"
            onClick={onAction}
            icon={type === 'search' ? <Search className="w-5 h-5" /> : 
                  type === 'matches' ? <Heart className="w-5 h-5" /> :
                  type === 'conversations' ? <MessageCircle className="w-5 h-5" /> :
                  <Settings className="w-5 h-5" />}
          >
            {finalActionLabel}
          </ModernButton>
        </div>
      )}

      {/* Decorative elements */}
      <div className="absolute top-1/4 left-1/4 w-2 h-2 bg-purple-300 rounded-full animate-pulse opacity-60"></div>
      <div className="absolute top-1/3 right-1/3 w-1 h-1 bg-pink-300 rounded-full animate-pulse opacity-60" style={{ animationDelay: '1s' }}></div>
      <div className="absolute bottom-1/4 left-1/3 w-1.5 h-1.5 bg-purple-300 rounded-full animate-pulse opacity-60" style={{ animationDelay: '2s' }}></div>
    </div>
  );
};

export default EmptyState;
