import { supabase } from '../lib/supabase';

/**
 * Utility function to check if conversation boundaries tables exist
 * This can be called from the browser console for debugging
 */
export async function checkConversationBoundariesTables() {
  try {
    console.log('🔍 Checking conversation boundaries tables...');
    
    // Test conversation_boundaries table
    const { data: boundariesData, error: boundariesError } = await supabase
      .from('conversation_boundaries')
      .select('count', { count: 'exact', head: true });
    
    if (boundariesError) {
      console.error('❌ conversation_boundaries table:', boundariesError.message);
    } else {
      console.log('✅ conversation_boundaries table exists');
    }
    
    // Test blocked_users table
    const { data: blockedData, error: blockedError } = await supabase
      .from('blocked_users')
      .select('count', { count: 'exact', head: true });
    
    if (blockedError) {
      console.error('❌ blocked_users table:', blockedError.message);
    } else {
      console.log('✅ blocked_users table exists');
    }
    
    // Test conversation_reports table
    const { data: reportsData, error: reportsError } = await supabase
      .from('conversation_reports')
      .select('count', { count: 'exact', head: true });
    
    if (reportsError) {
      console.error('❌ conversation_reports table:', reportsError.message);
    } else {
      console.log('✅ conversation_reports table exists');
    }
    
    const allTablesExist = !boundariesError && !blockedError && !reportsError;
    
    if (allTablesExist) {
      console.log('🎉 All conversation boundaries tables are set up correctly!');
    } else {
      console.log('⚠️ Some tables are missing. Please run the SQL migration script.');
    }
    
    return {
      conversation_boundaries: !boundariesError,
      blocked_users: !blockedError,
      conversation_reports: !reportsError,
      allTablesExist
    };
    
  } catch (err) {
    console.error('❌ Error checking tables:', err);
    return {
      conversation_boundaries: false,
      blocked_users: false,
      conversation_reports: false,
      allTablesExist: false
    };
  }
}

/**
 * Create a test blocked user relationship for testing
 * Usage: createTestBlockedUser('user1-id', 'user2-id', 'Testing block functionality')
 */
export async function createTestBlockedUser(blockerId: string, blockedId: string, reason?: string) {
  try {
    console.log(`🧪 Creating test blocked user relationship...`);
    
    const { data, error } = await supabase
      .from('blocked_users')
      .insert({
        blocker_id: blockerId,
        blocked_id: blockedId,
        reason: reason || 'Test block'
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Failed to create test blocked user:', error.message);
      return null;
    }
    
    console.log('✅ Test blocked user created:', data);
    return data;
    
  } catch (err) {
    console.error('❌ Error creating test blocked user:', err);
    return null;
  }
}

/**
 * Create default conversation boundaries for a user
 * Usage: createTestBoundaries('user-id')
 */
export async function createTestBoundaries(userId: string) {
  try {
    console.log(`🧪 Creating test conversation boundaries for user ${userId}...`);
    
    const { data, error } = await supabase
      .from('conversation_boundaries')
      .insert({
        user_id: userId,
        allow_messages_from: 'everyone',
        require_introduction: false,
        auto_decline_explicit: true,
        response_time_expectation: 'flexible',
        availability_timezone: 'UTC',
        block_explicit_content: true,
        block_personal_questions_early: false,
        block_meeting_requests_early: false,
        block_contact_sharing_early: true,
        custom_boundaries: ['No inappropriate messages', 'Be respectful']
      })
      .select()
      .single();
    
    if (error) {
      console.error('❌ Failed to create test boundaries:', error.message);
      return null;
    }
    
    console.log('✅ Test boundaries created:', data);
    return data;
    
  } catch (err) {
    console.error('❌ Error creating test boundaries:', err);
    return null;
  }
}

/**
 * Clean up test data
 * Usage: cleanupTestData('user-id')
 */
export async function cleanupTestData(userId: string) {
  try {
    console.log(`🧹 Cleaning up test data for user ${userId}...`);
    
    // Remove test boundaries
    const { error: boundariesError } = await supabase
      .from('conversation_boundaries')
      .delete()
      .eq('user_id', userId);
    
    if (boundariesError) {
      console.error('❌ Failed to clean up boundaries:', boundariesError.message);
    } else {
      console.log('✅ Test boundaries cleaned up');
    }
    
    // Remove test blocked users
    const { error: blockedError } = await supabase
      .from('blocked_users')
      .delete()
      .eq('blocker_id', userId);
    
    if (blockedError) {
      console.error('❌ Failed to clean up blocked users:', blockedError.message);
    } else {
      console.log('✅ Test blocked users cleaned up');
    }
    
    console.log('🎉 Test data cleanup complete!');
    
  } catch (err) {
    console.error('❌ Error cleaning up test data:', err);
  }
}

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
  (window as any).checkConversationBoundariesTables = checkConversationBoundariesTables;
  (window as any).createTestBlockedUser = createTestBlockedUser;
  (window as any).createTestBoundaries = createTestBoundaries;
  (window as any).cleanupTestData = cleanupTestData;
  
  console.log(`
🛠️ Conversation Boundaries Debug Tools Available:

• checkConversationBoundariesTables() - Check if tables exist
• createTestBoundaries('user-id') - Create test boundaries
• createTestBlockedUser('blocker-id', 'blocked-id', 'reason') - Create test block
• cleanupTestData('user-id') - Clean up test data

Example usage:
await checkConversationBoundariesTables()
  `);
}
