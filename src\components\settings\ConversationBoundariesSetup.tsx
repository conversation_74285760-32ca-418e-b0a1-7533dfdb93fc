import React, { useState } from 'react';
import { Alert<PERSON>riangle, <PERSON>ting<PERSON>, Shield, RefreshCw } from 'lucide-react';
import { useToast } from '../../hooks/useToast';

interface ConversationBoundariesSetupProps {
  onClose: () => void;
}

const ConversationBoundariesSetup: React.FC<ConversationBoundariesSetupProps> = ({ onClose }) => {
  const [isRetrying, setIsRetrying] = useState(false);
  const { showError, showSuccess, showLoading, dismiss } = useToast();

  const handleRetry = async () => {
    setIsRetrying(true);
    const loadingToastId = showLoading('Checking system status...');

    try {
      // Wait a moment to simulate checking
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Refresh the page to retry
      window.location.reload();
    } catch (err) {
      showError('Unable to connect to the system. Please try again later.');
    } finally {
      setIsRetrying(false);
      dismiss(loadingToastId);
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
      <div className="bg-white rounded-xl shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center">
            <Shield className="w-8 h-8 text-orange-500 mr-3" />
            <div>
              <h2 className="text-2xl font-bold text-gray-800">Feature Temporarily Unavailable</h2>
              <p className="text-gray-600 mt-1">Conversation boundaries are being set up</p>
            </div>
          </div>
        </div>

        {/* Content */}
        <div className="p-6">
          <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-6">
            <div className="flex items-start">
              <AlertTriangle className="w-5 h-5 text-orange-600 mr-3 mt-0.5 flex-shrink-0" />
              <div>
                <h3 className="font-medium text-orange-800 mb-2">System Setup in Progress</h3>
                <p className="text-orange-700 text-sm">
                  The conversation boundaries feature is currently being configured. This includes user blocking,
                  communication preferences, and content filtering capabilities.
                </p>
              </div>
            </div>
          </div>

          <div className="space-y-6">
            <div className="text-center py-8">
              <Settings className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-800 mb-2">Coming Soon</h3>
              <p className="text-gray-600 mb-6">
                We're setting up advanced conversation management tools to help you control who can contact you
                and how you interact with other users.
              </p>

              <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 text-left">
                <h4 className="font-medium text-blue-800 mb-2">What you'll be able to do:</h4>
                <ul className="text-blue-700 text-sm space-y-1">
                  <li>• Block and unblock users</li>
                  <li>• Set who can message you</li>
                  <li>• Filter inappropriate content</li>
                  <li>• Report problematic behavior</li>
                  <li>• Customize your availability</li>
                </ul>
              </div>
            </div>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-between p-6 border-t border-gray-200">
          <button
            onClick={handleRetry}
            disabled={isRetrying}
            className="flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            <RefreshCw className={`w-4 h-4 ${isRetrying ? 'animate-spin' : ''}`} />
            {isRetrying ? 'Checking...' : 'Check Again'}
          </button>

          <button
            onClick={onClose}
            className="px-6 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700 transition-colors"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  );
};

export default ConversationBoundariesSetup;
